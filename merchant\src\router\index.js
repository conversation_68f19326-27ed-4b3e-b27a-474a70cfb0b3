import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/components/Layout/index.vue'
import store from '@/store'
import { checkRoutePermission } from '@/utils/permission'
import { Message } from 'element-ui'

Vue.use(VueRouter)

// 基础路由（不需要权限）
export const constantRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录', hidden: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/merchant/Register.vue'),
    meta: { title: '商户注册', hidden: true }
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '404', hidden: true }
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/error/403.vue'),
    meta: { title: '403', hidden: true }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '首页', icon: 'el-icon-s-home' }
      }
    ]
  }
]

// 动态路由（需要权限）
export const asyncRoutes = [
  {
    path: '/merchant',
    component: Layout,
    meta: { 
      title: '商户管理', 
      icon: 'el-icon-user',
      permission: ['merchant:view']
    },
    children: [
      {
        path: 'kyc-status',
        name: 'KycStatus',
        component: () => import('@/views/merchant/KycStatus.vue'),
        meta: { 
          title: 'KYC状态',
          permission: ['merchant:view']
        }
      },
      {
        path: 'list',
        name: 'MerchantList',
        component: () => import('@/views/merchant/List.vue'),
        meta: { 
          title: '商户列表',
          permission: ['merchant:view']
        }
      },
      {
        path: 'create',
        name: 'MerchantCreate',
        component: () => import('@/views/merchant/Create.vue'),
        meta: { 
          title: '新增商户',
          permission: ['merchant:create']
        }
      },
      {
        path: 'edit/:id',
        name: 'MerchantEdit',
        component: () => import('@/views/merchant/Edit.vue'),
        meta: { 
          title: '编辑商户',
          permission: ['merchant:edit'],
          hidden: true
        }
      }
    ]
  },
  {
    path: '/order',
    component: Layout,
    meta: { 
      title: '订单管理', 
      icon: 'el-icon-document',
      permission: ['order:view']
    },
    children: [
      {
        path: 'list',
        name: 'OrderList',
        component: () => import('@/views/order/List.vue'),
        meta: { 
          title: '订单列表',
          permission: ['order:view']
        }
      },
      {
        path: 'detail/:id',
        name: 'OrderDetail',
        component: () => import('@/views/order/Detail.vue'),
        meta: { 
          title: '订单详情',
          permission: ['order:view'],
          hidden: true
        }
      }
    ]
  },
  {
    path: '/settlement',
    component: Layout,
    meta: { 
      title: '结算管理', 
      icon: 'el-icon-money',
      permission: ['settlement:view']
    },
    children: [
      {
        path: 'list',
        name: 'SettlementList',
        component: () => import('@/views/settlement/List.vue'),
        meta: { 
          title: '结算列表',
          permission: ['settlement:view']
        }
      }
    ]
  },
  {
    path: '/channel',
    component: Layout,
    meta: { 
      title: '通道管理', 
      icon: 'el-icon-connection',
      permission: ['channel:view']
    },
    children: [
      {
        path: 'list',
        name: 'ChannelList',
        component: () => import('@/views/channel/List.vue'),
        meta: { 
          title: '通道列表',
          permission: ['channel:view']
        }
      },
      {
        path: 'config',
        name: 'ChannelConfig',
        component: () => import('@/views/channel/Config.vue'),
        meta: { 
          title: '通道配置',
          permission: ['channel:config']
        }
      }
    ]
  },
  {
    path: '/wallet',
    component: Layout,
    meta: { 
      title: '钱包管理', 
      icon: 'el-icon-wallet',
      permission: ['wallet:view']
    },
    children: [
      {
        path: 'balance',
        name: 'WalletBalance',
        component: () => import('@/views/wallet/Balance.vue'),
        meta: { 
          title: '余额查询',
          permission: ['wallet:view']
        }
      },
      {
        path: 'recharge',
        name: 'WalletRecharge',
        component: () => import('@/views/wallet/Recharge.vue'),
        meta: { 
          title: 'USDT充值',
          permission: ['wallet:recharge']
        }
      }
    ]
  },
  {
    path: '/profile',
    component: Layout,
    meta: { title: '个人中心', hidden: true },
    children: [
      {
        path: 'index',
        name: 'Profile',
        component: () => import('@/views/profile/Index.vue'),
        meta: { title: '个人中心' }
      }
    ]
  },
  // 404页面必须放在最后
  { path: '*', redirect: '/404', meta: { hidden: true }}
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes: constantRoutes
})

// 白名单路由（不需要登录）
const whiteList = ['/login', '/register', '/404', '/403']

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const token = store.getters['user/token']
  
  if (token) {
    if (to.path === '/login') {
      // 已登录，跳转到首页
      next({ path: '/' })
    } else {
      // 检查是否已获取用户信息
      const userInfo = store.getters['user/userInfo']
      if (!userInfo.id) {
        try {
          // 获取用户信息和权限
          await store.dispatch('user/getUserInfo')
          
          // 生成动态路由
          const accessRoutes = await store.dispatch('permission/generateRoutes')
          
          // 动态添加路由
          router.addRoutes(accessRoutes)
          
          // 确保addRoutes已完成，重新导航到目标路由
          next({ ...to, replace: true })
        } catch (error) {
          console.error('获取用户信息失败:', error)
          // 获取用户信息失败，清除token并跳转到登录页
          await store.dispatch('user/logout')
          Message.error('获取用户信息失败，请重新登录')
          next(`/login?redirect=${to.path}`)
        }
      } else {
        // 检查路由权限
        if (checkRoutePermission(to, userInfo)) {
          next()
        } else {
          Message.error('您没有访问该页面的权限')
          next('/403')
        }
      }
    }
  } else {
    // 未登录
    if (whiteList.indexOf(to.path) !== -1) {
      // 在白名单中，直接进入
      next()
    } else {
      // 其他页面重定向到登录页
      next(`/login?redirect=${to.path}`)
    }
  }
})

export default router