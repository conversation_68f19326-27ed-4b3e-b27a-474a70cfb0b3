/**
 * 用户反馈系统 - 统一的消息提示、确认对话框和通知管理
 */

import { Message, MessageBox, Notification } from 'element-ui'

class FeedbackManager {
  constructor() {
    this.messageQueue = []
    this.notificationQueue = []
    this.isProcessing = false
  }

  // 成功消息
  success(message, options = {}) {
    return Message({
      type: 'success',
      message,
      duration: options.duration || 3000,
      showClose: options.showClose || false,
      ...options
    })
  }

  // 错误消息
  error(message, options = {}) {
    return Message({
      type: 'error',
      message,
      duration: options.duration || 5000,
      showClose: options.showClose || true,
      ...options
    })
  }

  // 警告消息
  warning(message, options = {}) {
    return Message({
      type: 'warning',
      message,
      duration: options.duration || 4000,
      showClose: options.showClose || false,
      ...options
    })
  }

  // 信息消息
  info(message, options = {}) {
    return Message({
      type: 'info',
      message,
      duration: options.duration || 3000,
      showClose: options.showClose || false,
      ...options
    })
  }

  // 确认对话框
  confirm(message, title = '确认', options = {}) {
    const defaultOptions = {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      center: false,
      ...options
    }

    return MessageBox.confirm(message, title, defaultOptions)
  }

  // 删除确认
  confirmDelete(message = '此操作将永久删除该数据，是否继续？', title = '删除确认') {
    return this.confirm(message, title, {
      type: 'warning',
      confirmButtonText: '删除',
      confirmButtonClass: 'el-button--danger'
    })
  }

  // 操作确认
  confirmAction(action, message, title = '操作确认') {
    return this.confirm(
      message || `确定要${action}吗？`,
      title,
      {
        type: 'info',
        confirmButtonText: action
      }
    )
  }

  // 输入对话框
  prompt(message, title = '输入', options = {}) {
    const defaultOptions = {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputType: 'text',
      inputValidator: null,
      inputErrorMessage: '输入不能为空',
      ...options
    }

    return MessageBox.prompt(message, title, defaultOptions)
  }

  // 通知
  notify(message, title = '通知', options = {}) {
    return Notification({
      title,
      message,
      type: options.type || 'info',
      duration: options.duration || 4500,
      position: options.position || 'top-right',
      ...options
    })
  }

  // 成功通知
  notifySuccess(message, title = '成功', options = {}) {
    return this.notify(message, title, { type: 'success', ...options })
  }

  // 错误通知
  notifyError(message, title = '错误', options = {}) {
    return this.notify(message, title, { type: 'error', duration: 0, ...options })
  }

  // 警告通知
  notifyWarning(message, title = '警告', options = {}) {
    return this.notify(message, title, { type: 'warning', ...options })
  }

  // 信息通知
  notifyInfo(message, title = '信息', options = {}) {
    return this.notify(message, title, { type: 'info', ...options })
  }

  // 批量操作反馈
  batchFeedback(results, successMessage = '操作完成', errorMessage = '部分操作失败') {
    const successCount = results.filter(r => r.status === 'fulfilled').length
    const errorCount = results.filter(r => r.status === 'rejected').length

    if (errorCount === 0) {
      this.success(`${successMessage}，共处理 ${successCount} 项`)
    } else if (successCount === 0) {
      this.error(`${errorMessage}，共 ${errorCount} 项失败`)
    } else {
      this.warning(`${successMessage} ${successCount} 项，${errorCount} 项失败`)
    }

    return { successCount, errorCount }
  }

  // 操作结果反馈
  operationResult(success, successMessage = '操作成功', errorMessage = '操作失败') {
    if (success) {
      this.success(successMessage)
    } else {
      this.error(errorMessage)
    }
  }

  // 表单验证反馈
  formValidation(isValid, errorMessage = '请检查表单填写是否正确') {
    if (!isValid) {
      this.error(errorMessage)
    }
    return isValid
  }

  // 网络错误反馈
  networkError(error) {
    let message = '网络请求失败'
    
    if (error.response) {
      switch (error.response.status) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          break
        case 403:
          message = '权限不足'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = `请求失败 (${error.response.status})`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.message) {
      message = error.message
    }

    this.error(message)
  }

  // 队列处理消息（防止消息过多）
  queueMessage(type, message, options = {}) {
    this.messageQueue.push({ type, message, options })
    
    if (!this.isProcessing) {
      this.processMessageQueue()
    }
  }

  async processMessageQueue() {
    this.isProcessing = true
    
    while (this.messageQueue.length > 0) {
      const { type, message, options } = this.messageQueue.shift()
      this[type](message, options)
      
      // 延迟处理下一个消息
      await new Promise(resolve => setTimeout(resolve, 500))
    }
    
    this.isProcessing = false
  }

  // 清除所有消息
  clearAll() {
    Message.closeAll()
    Notification.closeAll()
    this.messageQueue = []
    this.notificationQueue = []
  }
}

// 创建全局实例
export const feedbackManager = new FeedbackManager()

// Vue 插件
export const FeedbackPlugin = {
  install(Vue) {
    // 添加到 Vue 原型
    Vue.prototype.$feedback = feedbackManager
    
    // 简化方法
    Vue.prototype.$success = feedbackManager.success.bind(feedbackManager)
    Vue.prototype.$error = feedbackManager.error.bind(feedbackManager)
    Vue.prototype.$warning = feedbackManager.warning.bind(feedbackManager)
    Vue.prototype.$info = feedbackManager.info.bind(feedbackManager)
    Vue.prototype.$confirm = feedbackManager.confirm.bind(feedbackManager)
    Vue.prototype.$notify = feedbackManager.notify.bind(feedbackManager)

    // 全局混入
    Vue.mixin({
      methods: {
        // 操作成功反馈
        handleSuccess(message = '操作成功', callback = null) {
          this.$success(message)
          if (callback && typeof callback === 'function') {
            callback()
          }
        },

        // 操作失败反馈
        handleError(error, defaultMessage = '操作失败') {
          const message = error?.message || error || defaultMessage
          this.$error(message)
        },

        // 确认删除
        async confirmDelete(message, callback) {
          try {
            await this.$confirm(
              message || '此操作将永久删除该数据，是否继续？',
              '删除确认',
              {
                type: 'warning',
                confirmButtonText: '删除',
                confirmButtonClass: 'el-button--danger'
              }
            )
            
            if (callback && typeof callback === 'function') {
              await callback()
            }
          } catch (error) {
            // 用户取消操作
          }
        },

        // 确认操作
        async confirmAction(action, message, callback) {
          try {
            await this.$confirm(
              message || `确定要${action}吗？`,
              '操作确认',
              {
                type: 'info',
                confirmButtonText: action
              }
            )
            
            if (callback && typeof callback === 'function') {
              await callback()
            }
          } catch (error) {
            // 用户取消操作
          }
        },

        // 处理异步操作
        async handleAsync(asyncFn, successMessage = '操作成功', errorMessage = '操作失败') {
          try {
            const result = await asyncFn()
            this.$success(successMessage)
            return result
          } catch (error) {
            this.handleError(error, errorMessage)
            throw error
          }
        }
      }
    })
  }
}

// 操作反馈装饰器
export function withFeedback(successMessage = '操作成功', errorMessage = '操作失败') {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function(...args) {
      try {
        const result = await originalMethod.apply(this, args)
        feedbackManager.success(successMessage)
        return result
      } catch (error) {
        feedbackManager.error(error.message || errorMessage)
        throw error
      }
    }

    return descriptor
  }
}

// 确认操作装饰器
export function withConfirm(message, title = '确认操作') {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function(...args) {
      try {
        await feedbackManager.confirm(message, title)
        return await originalMethod.apply(this, args)
      } catch (error) {
        // 用户取消操作
        throw new Error('cancel')
      }
    }

    return descriptor
  }
}

export default {
  FeedbackManager,
  feedbackManager,
  FeedbackPlugin,
  withFeedback,
  withConfirm
}