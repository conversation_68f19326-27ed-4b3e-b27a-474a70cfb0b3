<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h2>商户控制台</h2>
      <p>欢迎使用 Jeepay 商户管理系统</p>
    </div>
    
    <!-- KYC状态卡片 -->
    <el-card class="kyc-status-card" v-if="merchantInfo.state !== 2">
      <div class="kyc-alert">
        <el-alert
          :title="kycAlertTitle"
          :type="kycAlertType"
          :description="kycAlertDesc"
          show-icon
          :closable="false"
        >
          <template slot="description">
            <div class="kyc-actions">
              <p>{{ kycAlertDesc }}</p>
              <el-button
                v-if="merchantInfo.state === 0"
                type="primary"
                size="small"
                @click="goToKyc"
              >
                立即认证
              </el-button>
              <el-button
                v-if="merchantInfo.state === 3"
                type="primary"
                size="small"
                @click="goToKyc"
              >
                重新提交
              </el-button>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>
    
    <!-- 数据统计 -->
    <div class="stats-grid">
      <el-card class="stats-card">
        <div class="stats-content">
          <div class="stats-icon">
            <i class="el-icon-money" style="color: #67C23A"></i>
          </div>
          <div class="stats-info">
            <div class="stats-value">¥ {{ formatMoney(stats.totalAmount) }}</div>
            <div class="stats-label">总交易金额</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stats-card">
        <div class="stats-content">
          <div class="stats-icon">
            <i class="el-icon-document" style="color: #409EFF"></i>
          </div>
          <div class="stats-info">
            <div class="stats-value">{{ stats.totalOrders }}</div>
            <div class="stats-label">总订单数</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stats-card">
        <div class="stats-content">
          <div class="stats-icon">
            <i class="el-icon-success" style="color: #67C23A"></i>
          </div>
          <div class="stats-info">
            <div class="stats-value">{{ stats.successOrders }}</div>
            <div class="stats-label">成功订单</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stats-card">
        <div class="stats-content">
          <div class="stats-icon">
            <i class="el-icon-wallet" style="color: #E6A23C"></i>
          </div>
          <div class="stats-info">
            <div class="stats-value">¥ {{ formatMoney(stats.balance) }}</div>
            <div class="stats-label">账户余额</div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 快捷操作 -->
    <el-card class="quick-actions">
      <div slot="header">
        <span>快捷操作</span>
      </div>
      <div class="actions-grid">
        <div class="action-item" @click="goToKyc">
          <i class="el-icon-user"></i>
          <span>KYC认证</span>
        </div>
        <div class="action-item" @click="goToOrders">
          <i class="el-icon-document"></i>
          <span>订单管理</span>
        </div>
        <div class="action-item" @click="goToSettlement">
          <i class="el-icon-money"></i>
          <span>结算管理</span>
        </div>
        <div class="action-item" @click="goToWallet">
          <i class="el-icon-wallet"></i>
          <span>钱包管理</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getMerchantInfo } from '@/api/merchant'

export default {
  name: 'Dashboard',
  data() {
    return {
      merchantInfo: {},
      stats: {
        totalAmount: 0,
        totalOrders: 0,
        successOrders: 0,
        balance: 0
      }
    }
  },
  computed: {
    kycAlertTitle() {
      const titleMap = {
        0: '待完成身份认证',
        1: '身份认证审核中',
        3: '身份认证未通过'
      }
      return titleMap[this.merchantInfo.state] || ''
    },
    kycAlertType() {
      const typeMap = {
        0: 'warning',
        1: 'info',
        3: 'error'
      }
      return typeMap[this.merchantInfo.state] || 'info'
    },
    kycAlertDesc() {
      const descMap = {
        0: '请完成身份认证以使用完整功能',
        1: '您的身份认证正在审核中，请耐心等待',
        3: '您的身份认证未通过，请重新提交认证资料'
      }
      return descMap[this.merchantInfo.state] || ''
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      try {
        const response = await getMerchantInfo()
        if (response.code === 0) {
          this.merchantInfo = response.data
        }
      } catch (error) {
        console.error('Fetch merchant info error:', error)
      }
      
      // 模拟统计数据
      this.stats = {
        totalAmount: 125680.50,
        totalOrders: 1234,
        successOrders: 1180,
        balance: 8520.30
      }
    },
    formatMoney(amount) {
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    goToKyc() {
      this.$router.push('/merchant/kyc-status')
    },
    goToOrders() {
      this.$router.push('/order/list')
    },
    goToSettlement() {
      this.$router.push('/settlement/list')
    },
    goToWallet() {
      this.$router.push('/wallet/balance')
    }
  }
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.dashboard-header {
  margin-bottom: 20px;
}

.dashboard-header h2 {
  color: #333;
  margin-bottom: 8px;
}

.dashboard-header p {
  color: #666;
  font-size: 14px;
}

.kyc-status-card {
  margin-bottom: 20px;
}

.kyc-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.kyc-actions p {
  margin: 0;
  flex: 1;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stats-icon {
  margin-right: 16px;
}

.stats-icon i {
  font-size: 32px;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #666;
}

.quick-actions {
  margin-bottom: 20px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.action-item:hover {
  border-color: #409EFF;
  color: #409EFF;
  transform: translateY(-2px);
}

.action-item i {
  font-size: 32px;
  margin-bottom: 8px;
}

.action-item span {
  font-size: 14px;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .kyc-actions {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .kyc-actions .el-button {
    margin-top: 10px;
  }
}
</style>