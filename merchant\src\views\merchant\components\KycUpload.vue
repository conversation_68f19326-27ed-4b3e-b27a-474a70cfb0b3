<template>
  <div class="kyc-upload">
    <el-form
      ref="kycForm"
      :model="kycForm"
      :rules="kycRules"
      label-width="120px"
    >
      <div class="upload-section">
        <h4>身份证照片</h4>
        <p class="upload-tip">请上传清晰的身份证正反面照片，支持 JPG、PNG 格式，文件大小不超过 5MB</p>
        
        <div class="upload-row">
          <div class="upload-item">
            <label>身份证正面</label>
            <el-form-item prop="id_card_front">
              <file-upload
                v-model="kycForm.id_card_front"
                placeholder="上传身份证正面"
                :max-size="5"
                @success="handleUploadSuccess"
              />
            </el-form-item>
          </div>
          
          <div class="upload-item">
            <label>身份证反面</label>
            <el-form-item prop="id_card_back">
              <file-upload
                v-model="kycForm.id_card_back"
                placeholder="上传身份证反面"
                :max-size="5"
                @success="handleUploadSuccess"
              />
            </el-form-item>
          </div>
        </div>
      </div>
      
      <div class="upload-section">
        <h4>营业执照</h4>
        <p class="upload-tip">请上传清晰的营业执照照片，支持 JPG、PNG、PDF 格式，文件大小不超过 5MB</p>
        
        <div class="upload-row">
          <div class="upload-item">
            <label>营业执照</label>
            <el-form-item prop="business_license">
              <file-upload
                v-model="kycForm.business_license"
                placeholder="上传营业执照"
                :max-size="5"
                @success="handleUploadSuccess"
              />
            </el-form-item>
          </div>
        </div>
      </div>
      
      <div class="upload-notice">
        <el-alert
          title="上传须知"
          type="info"
          :closable="false"
          show-icon
        >
          <div slot="description">
            <p>1. 请确保上传的证件照片清晰可见，信息完整</p>
            <p>2. 身份证需要正反面都上传</p>
            <p>3. 营业执照需要在有效期内</p>
            <p>4. 上传的资料将用于身份验证，请确保真实有效</p>
          </div>
        </el-alert>
      </div>
      
      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ submitting ? '提交中...' : '提交审核' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import FileUpload from '@/components/Upload/FileUpload.vue'
import { submitKycDocuments } from '@/api/merchant'

export default {
  name: 'KycUpload',
  components: {
    FileUpload
  },
  data() {
    return {
      submitting: false,
      kycForm: {
        id_card_front: '',
        id_card_back: '',
        business_license: ''
      },
      kycRules: {
        id_card_front: [
          { required: true, message: '请上传身份证正面', trigger: 'change' }
        ],
        id_card_back: [
          { required: true, message: '请上传身份证反面', trigger: 'change' }
        ],
        business_license: [
          { required: true, message: '请上传营业执照', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    handleUploadSuccess(data) {
      console.log('File uploaded:', data)
    },
    handleSubmit() {
      this.$refs.kycForm.validate(async (valid) => {
        if (!valid) return

        this.submitting = true
        try {
          const response = await submitKycDocuments(this.kycForm)
          if (response.code === 0) {
            this.$message.success('KYC资料提交成功！')
            this.$emit('success')
            this.resetForm()
          } else {
            this.$message.error(response.msg || '提交失败')
          }
        } catch (error) {
          console.error('Submit KYC error:', error)
          this.$message.error('提交失败，请稍后重试')
        } finally {
          this.submitting = false
        }
      })
    },
    handleCancel() {
      this.$emit('cancel')
      this.resetForm()
    },
    resetForm() {
      this.kycForm = {
        id_card_front: '',
        id_card_back: '',
        business_license: ''
      }
      this.$refs.kycForm && this.$refs.kycForm.resetFields()
    }
  }
}
</script>

<style scoped>
.kyc-upload {
  padding: 20px 0;
}

.upload-section {
  margin-bottom: 30px;
}

.upload-section h4 {
  margin-bottom: 8px;
  color: #333;
  font-size: 16px;
}

.upload-tip {
  margin-bottom: 20px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.upload-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.upload-item {
  flex: 1;
  min-width: 200px;
}

.upload-item label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.upload-notice {
  margin-bottom: 30px;
}

.upload-notice .el-alert__description p {
  margin: 4px 0;
  line-height: 1.5;
}

.form-actions {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.form-actions .el-button {
  margin-left: 10px;
}

@media (max-width: 768px) {
  .upload-row {
    flex-direction: column;
  }
  
  .upload-item {
    min-width: auto;
  }
  
  .form-actions {
    text-align: center;
  }
  
  .form-actions .el-button {
    margin: 5px;
  }
}
</style>