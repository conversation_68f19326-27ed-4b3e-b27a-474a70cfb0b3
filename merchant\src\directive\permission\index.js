import { hasPermission, hasRole } from '@/utils/permission'

// 权限指令
const permission = {
  inserted(el, binding, vnode) {
    const { value } = binding
    
    if (value && !hasPermission(value)) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  },
  update(el, binding, vnode) {
    const { value, oldValue } = binding
    
    if (value !== oldValue) {
      if (value && !hasPermission(value)) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  }
}

// 角色指令
const role = {
  inserted(el, binding, vnode) {
    const { value } = binding
    
    if (value && !hasRole(value)) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  },
  update(el, binding, vnode) {
    const { value, oldValue } = binding
    
    if (value !== oldValue) {
      if (value && !hasRole(value)) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  }
}

export default {
  install(Vue) {
    Vue.directive('permission', permission)
    Vue.directive('role', role)
  }
}