<template>
  <div class="channel-config">
    <el-form
      ref="configForm"
      :model="configForm"
      :rules="configRules"
      label-width="120px"
    >
      <div class="config-section">
        <h4>基本配置</h4>
        <el-form-item label="通道名称">
          <el-input v-model="channelInfo.channel_name" disabled></el-input>
        </el-form-item>
        
        <el-form-item label="支付方式">
          <el-input v-model="payMethodText" disabled></el-input>
        </el-form-item>
        
        <el-form-item label="通道状态" prop="status">
          <el-radio-group v-model="configForm.status">
            <el-radio :label="0">禁用</el-radio>
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">维护中</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="费率" prop="rate">
          <el-input
            v-model="configForm.rate"
            placeholder="请输入费率"
            type="number"
            :min="0"
            :max="1"
            :step="0.001"
          >
            <template slot="append">%</template>
          </el-input>
          <div class="form-tips">
            <p>费率范围：0% - 100%</p>
          </div>
        </el-form-item>
        
        <el-form-item label="最小金额" prop="min_amount">
          <el-input
            v-model="configForm.min_amount"
            placeholder="请输入最小金额"
            type="number"
            :min="0"
          >
            <template slot="prepend">¥</template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="最大金额" prop="max_amount">
          <el-input
            v-model="configForm.max_amount"
            placeholder="请输入最大金额"
            type="number"
            :min="0"
          >
            <template slot="prepend">¥</template>
          </el-input>
        </el-form-item>
      </div>
      
      <div class="config-section">
        <h4>通道参数</h4>
        <div class="config-params">
          <div
            v-for="(param, key) in configParams"
            :key="key"
            class="param-item"
          >
            <el-form-item :label="param.label" :prop="`config.${key}`">
              <el-input
                v-if="param.type === 'text'"
                v-model="configForm.config[key]"
                :placeholder="param.placeholder"
                :type="param.secret ? 'password' : 'text'"
                show-password
              ></el-input>
              <el-select
                v-else-if="param.type === 'select'"
                v-model="configForm.config[key]"
                :placeholder="param.placeholder"
                style="width: 100%"
              >
                <el-option
                  v-for="option in param.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>
              <div v-if="param.description" class="param-desc">
                {{ param.description }}
              </div>
            </el-form-item>
          </div>
        </div>
      </div>
      
      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ submitting ? '保存中...' : '保存配置' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { updateChannelConfig, PAY_METHOD } from '@/api/channel'

export default {
  name: 'ChannelConfig',
  props: {
    channelInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      submitting: false,
      configForm: {
        status: 1,
        rate: 0,
        min_amount: 0,
        max_amount: 0,
        config: {}
      },
      configRules: {
        status: [
          { required: true, message: '请选择通道状态', trigger: 'change' }
        ],
        rate: [
          { required: true, message: '请输入费率', trigger: 'blur' },
          { type: 'number', min: 0, max: 100, message: '费率范围为0-100%', trigger: 'blur' }
        ],
        min_amount: [
          { required: true, message: '请输入最小金额', trigger: 'blur' },
          { type: 'number', min: 0, message: '最小金额不能小于0', trigger: 'blur' }
        ],
        max_amount: [
          { required: true, message: '请输入最大金额', trigger: 'blur' },
          { type: 'number', min: 0, message: '最大金额不能小于0', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    payMethodText() {
      return PAY_METHOD[this.channelInfo.pay_method]?.text || '未知'
    },
    configParams() {
      // 根据支付方式返回不同的配置参数
      const paramsMap = {
        1: { // 外卡支付
          merchant_id: {
            label: '商户ID',
            type: 'text',
            placeholder: '请输入商户ID',
            description: '支付通道分配的商户ID'
          },
          secret_key: {
            label: '密钥',
            type: 'text',
            secret: true,
            placeholder: '请输入密钥',
            description: '用于签名验证的密钥'
          },
          api_url: {
            label: 'API地址',
            type: 'text',
            placeholder: '请输入API地址',
            description: '支付接口地址'
          },
          currency: {
            label: '币种',
            type: 'select',
            placeholder: '请选择币种',
            options: [
              { label: 'USD', value: 'USD' },
              { label: 'EUR', value: 'EUR' },
              { label: 'GBP', value: 'GBP' }
            ]
          }
        },
        2: { // USDT支付
          wallet_address: {
            label: '钱包地址',
            type: 'text',
            placeholder: '请输入USDT钱包地址',
            description: 'USDT收款钱包地址'
          },
          network: {
            label: '网络类型',
            type: 'select',
            placeholder: '请选择网络',
            options: [
              { label: 'TRC20', value: 'TRC20' },
              { label: 'ERC20', value: 'ERC20' }
            ]
          },
          api_key: {
            label: 'API密钥',
            type: 'text',
            secret: true,
            placeholder: '请输入API密钥',
            description: '区块链查询API密钥'
          }
        },
        3: { // 本地支付
          app_id: {
            label: '应用ID',
            type: 'text',
            placeholder: '请输入应用ID',
            description: '支付平台分配的应用ID'
          },
          private_key: {
            label: '私钥',
            type: 'text',
            secret: true,
            placeholder: '请输入私钥',
            description: 'RSA私钥用于签名'
          },
          public_key: {
            label: '公钥',
            type: 'text',
            placeholder: '请输入公钥',
            description: '平台公钥用于验签'
          },
          gateway_url: {
            label: '网关地址',
            type: 'text',
            placeholder: '请输入网关地址',
            description: '支付网关地址'
          }
        }
      }
      return paramsMap[this.channelInfo.pay_method] || {}
    }
  },
  watch: {
    channelInfo: {
      handler(newVal) {
        if (newVal && newVal.id) {
          this.initForm()
        }
      },
      immediate: true
    }
  },
  methods: {
    initForm() {
      this.configForm = {
        status: this.channelInfo.status || 1,
        rate: this.channelInfo.rate * 100 || 0,
        min_amount: this.channelInfo.min_amount || 0,
        max_amount: this.channelInfo.max_amount || 0,
        config: { ...this.channelInfo.config } || {}
      }
    },
    handleSubmit() {
      this.$refs.configForm.validate(async (valid) => {
        if (!valid) return

        this.submitting = true
        try {
          const data = {
            ...this.configForm,
            rate: this.configForm.rate / 100 // 转换为小数
          }
          const response = await updateChannelConfig(this.channelInfo.id, data)
          if (response.code === 0) {
            this.$emit('success')
          } else {
            this.$message.error(response.msg || '保存失败')
          }
        } catch (error) {
          console.error('Update channel config error:', error)
          this.$message.error('保存失败，请稍后重试')
        } finally {
          this.submitting = false
        }
      })
    },
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script><st
yle scoped>
.channel-config {
  padding: 20px 0;
}

.config-section {
  margin-bottom: 30px;
}

.config-section h4 {
  margin-bottom: 16px;
  color: #333;
  font-size: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.form-tips {
  margin-top: 8px;
}

.form-tips p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.config-params {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
}

.param-item {
  margin-bottom: 20px;
}

.param-item:last-child {
  margin-bottom: 0;
}

.param-desc {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.form-actions {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.form-actions .el-button {
  margin-left: 10px;
}

@media (max-width: 768px) {
  .form-actions {
    text-align: center;
  }
  
  .form-actions .el-button {
    margin: 5px;
  }
}
</style>