<template>
  <div class="upload-container">
    <el-upload
      :action="uploadUrl"
      :headers="headers"
      :data="uploadData"
      :multiple="multiple"
      :drag="drag"
      :accept="accept"
      :list-type="listType"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-progress="handleProgress"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :disabled="disabled"
      :limit="limit"
      :on-exceed="handleExceed"
    >
      <div v-if="drag" class="upload-dragger">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip" v-if="tip">{{ tip }}</div>
      </div>
      <template v-else>
        <el-button v-if="listType !== 'picture-card'" size="small" type="primary" :loading="uploading">
          <i class="el-icon-upload el-icon--right"></i>
          {{ buttonText }}
        </el-button>
        <i v-else class="el-icon-plus"></i>
        <div class="el-upload__tip" slot="tip" v-if="tip">{{ tip }}</div>
      </template>
    </el-upload>

    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="previewVisible" title="图片预览" width="800px" center>
      <img :src="previewUrl" style="width: 100%" alt="preview">
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Upload',
  props: {
    // 上传地址
    action: {
      type: String,
      default: '/api/upload'
    },
    // 上传的文件字段名
    name: {
      type: String,
      default: 'file'
    },
    // 上传时附带的额外参数
    data: {
      type: Object,
      default: () => ({})
    },
    // 设置上传的请求头部
    headers: {
      type: Object,
      default: () => ({})
    },
    // 是否支持多选文件
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否启用拖拽上传
    drag: {
      type: Boolean,
      default: false
    },
    // 接受上传的文件类型
    accept: {
      type: String,
      default: ''
    },
    // 文件列表的类型
    listType: {
      type: String,
      default: 'text' // text/picture/picture-card
    },
    // 文件列表
    fileList: {
      type: Array,
      default: () => []
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 最大允许上传个数
    limit: {
      type: Number,
      default: 0
    },
    // 文件大小限制(MB)
    maxSize: {
      type: Number,
      default: 10
    },
    // 按钮文字
    buttonText: {
      type: String,
      default: '点击上传'
    },
    // 提示文字
    tip: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      uploading: false,
      previewVisible: false,
      previewUrl: ''
    }
  },
  computed: {
    uploadUrl() {
      return this.action
    },
    uploadData() {
      return {
        ...this.data
      }
    }
  },
  methods: {
    // 上传文件之前的钩子
    beforeUpload(file) {
      // 检查文件大小
      if (this.maxSize && file.size / 1024 / 1024 > this.maxSize) {
        this.$message.error(`文件大小不能超过 ${this.maxSize}MB!`)
        return false
      }
      
      // 检查文件类型
      if (this.accept) {
        const acceptTypes = this.accept.split(',').map(type => type.trim())
        const fileType = file.type
        const fileName = file.name
        const fileExt = fileName.substring(fileName.lastIndexOf('.'))
        
        const isValidType = acceptTypes.some(type => {
          if (type.startsWith('.')) {
            return fileExt === type
          } else {
            return fileType.includes(type.replace('*', ''))
          }
        })
        
        if (!isValidType) {
          this.$message.error(`只能上传 ${this.accept} 格式的文件!`)
          return false
        }
      }
      
      this.uploading = true
      this.$emit('before-upload', file)
      return true
    },
    
    // 文件上传成功时的钩子
    handleSuccess(response, file, fileList) {
      this.uploading = false
      this.$emit('success', response, file, fileList)
      this.$message.success('上传成功!')
    },
    
    // 文件上传失败时的钩子
    handleError(err, file, fileList) {
      this.uploading = false
      this.$emit('error', err, file, fileList)
      this.$message.error('上传失败!')
    },
    
    // 文件上传时的钩子
    handleProgress(event, file, fileList) {
      this.$emit('progress', event, file, fileList)
    },
    
    // 文件列表移除文件时的钩子
    handleRemove(file, fileList) {
      this.$emit('remove', file, fileList)
    },
    
    // 点击文件列表中已上传的文件时的钩子
    handlePreview(file) {
      if (file.url && (file.url.includes('.jpg') || file.url.includes('.png') || file.url.includes('.jpeg'))) {
        this.previewUrl = file.url
        this.previewVisible = true
      } else {
        window.open(file.url)
      }
      this.$emit('preview', file)
    },
    
    // 文件超出个数限制时的钩子
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
      this.$emit('exceed', files, fileList)
    }
  }
}
</script>

<style scoped>
.upload-container {
  width: 100%;
}

.upload-dragger {
  text-align: center;
  padding: 40px 0;
}

.upload-dragger i {
  font-size: 67px;
  color: #C0C4CC;
  margin-bottom: 16px;
}
</style>