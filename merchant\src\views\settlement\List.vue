<template>
  <div class="settlement-list">
    <div class="page-header">
      <h2>结算管理</h2>
      <p>查看和管理结算记录</p>
    </div>
    
    <!-- 可结算金额卡片 -->
    <el-card class="available-card">
      <div class="available-content">
        <div class="available-info">
          <div class="available-amount">
            <span class="amount-label">可结算金额</span>
            <span class="amount-value">¥{{ formatMoney(availableAmount) }}</span>
          </div>
          <div class="available-desc">
            <p>最小结算金额：¥100.00</p>
            <p>结算手续费：2%</p>
          </div>
        </div>
        <div class="available-actions">
          <el-button
            type="primary"
            size="large"
            :disabled="availableAmount < 100"
            @click="showApplyDialog"
          >
            申请结算
          </el-button>
        </div>
      </div>
    </el-card>
    
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form
        ref="searchForm"
        :model="searchForm"
        :inline="true"
        label-width="80px"
        class="search-form"
      >
        <el-form-item label="结算单号">
          <el-input
            v-model="searchForm.settle_id"
            placeholder="请输入结算单号"
            clearable
            style="width: 200px"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="结算状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="申请时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 350px"
          ></el-date-picker>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 结算统计 -->
    <div class="stats-row">
      <el-card class="stats-item">
        <div class="stats-content">
          <div class="stats-value">{{ stats.totalCount }}</div>
          <div class="stats-label">总结算次数</div>
        </div>
      </el-card>
      <el-card class="stats-item">
        <div class="stats-content">
          <div class="stats-value">¥{{ formatMoney(stats.totalAmount) }}</div>
          <div class="stats-label">总结算金额</div>
        </div>
      </el-card>
      <el-card class="stats-item">
        <div class="stats-content">
          <div class="stats-value">{{ stats.successCount }}</div>
          <div class="stats-label">成功结算</div>
        </div>
      </el-card>
      <el-card class="stats-item">
        <div class="stats-content">
          <div class="stats-value">¥{{ formatMoney(stats.pendingAmount) }}</div>
          <div class="stats-label">待结算金额</div>
        </div>
      </el-card>
    </div>
    
    <!-- 结算列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="settlementList"
        stripe
        border
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="settle_id" label="结算单号" width="180" fixed="left">
          <template slot-scope="scope">
            <el-link type="primary" @click="viewDetail(scope.row)">
              {{ scope.row.settle_id }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="mch_no" label="商户号" width="120"></el-table-column>
        
        <el-table-column prop="amount" label="结算金额" width="120" sortable="custom">
          <template slot-scope="scope">
            <span class="amount-text">¥{{ formatMoney(scope.row.amount) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="fee" label="手续费" width="100">
          <template slot-scope="scope">
            <span class="fee-text">¥{{ formatMoney(scope.row.fee) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="actual_amount" label="实际到账" width="120">
          <template slot-scope="scope">
            <span class="actual-amount">¥{{ formatMoney(scope.row.actual_amount) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="结算状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="bank_name" label="收款银行" width="120">
          <template slot-scope="scope">
            {{ scope.row.bank_name || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="申请时间" width="160" sortable="custom">
          <template slot-scope="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="updated_at" label="更新时间" width="160" sortable="custom">
          <template slot-scope="scope">
            {{ formatTime(scope.row.updated_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="viewDetail(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        ></el-pagination>
      </div>
    </el-card>
    
    <!-- 申请结算对话框 -->
    <el-dialog
      title="申请结算"
      :visible.sync="applyDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <settlement-apply
        ref="settlementApply"
        :available-amount="availableAmount"
        @success="handleApplySuccess"
        @cancel="applyDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script>
import { 
  getSettlementList, 
  getSettlementStats, 
  getAvailableAmount,
  SETTLEMENT_STATUS, 
  getSettlementStatusOptions 
} from '@/api/settlement'
import SettlementApply from './components/SettlementApply.vue'

export default {
  name: 'SettlementList',
  components: {
    SettlementApply
  },
  data() {
    return {
      loading: false,
      settlementList: [],
      availableAmount: 0,
      applyDialogVisible: false,
      searchForm: {
        settle_id: '',
        status: '',
        mch_no: ''
      },
      dateRange: [],
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      sortField: '',
      sortOrder: '',
      stats: {
        totalCount: 0,
        totalAmount: 0,
        successCount: 0,
        pendingAmount: 0
      }
    }
  },
  computed: {
    statusOptions() {
      return getSettlementStatusOptions()
    },
    searchParams() {
      const params = {
        page: this.pagination.page,
        size: this.pagination.size,
        ...this.searchForm
      }
      
      if (this.dateRange && this.dateRange.length === 2) {
        params.created_at_start = this.dateRange[0]
        params.created_at_end = this.dateRange[1]
      }
      
      if (this.sortField) {
        params.sort_field = this.sortField
        params.sort_order = this.sortOrder
      }
      
      return params
    }
  },
  created() {
    this.fetchSettlementList()
    this.fetchStats()
    this.fetchAvailableAmount()
  },
  methods: {
    async fetchSettlementList() {
      this.loading = true
      try {
        const response = await getSettlementList(this.searchParams)
        if (response.code === 0) {
          this.settlementList = response.data.list || []
          this.pagination.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || '获取结算列表失败')
        }
      } catch (error) {
        console.error('Fetch settlement list error:', error)
        this.$message.error('获取结算列表失败')
      } finally {
        this.loading = false
      }
    },
    async fetchStats() {
      try {
        const params = {}
        if (this.dateRange && this.dateRange.length === 2) {
          params.date_start = this.dateRange[0].split(' ')[0]
          params.date_end = this.dateRange[1].split(' ')[0]
        }
        
        const response = await getSettlementStats(params)
        if (response.code === 0) {
          this.stats = response.data
        }
      } catch (error) {
        console.error('Fetch stats error:', error)
      }
    },
    async fetchAvailableAmount() {
      try {
        const response = await getAvailableAmount()
        if (response.code === 0) {
          this.availableAmount = response.data.amount || 0
        }
      } catch (error) {
        console.error('Fetch available amount error:', error)
      }
    },
    handleSearch() {
      this.pagination.page = 1
      this.fetchSettlementList()
      this.fetchStats()
    },
    handleReset() {
      this.searchForm = {
        settle_id: '',
        status: '',
        mch_no: ''
      }
      this.dateRange = []
      this.sortField = ''
      this.sortOrder = ''
      this.pagination.page = 1
      this.fetchSettlementList()
      this.fetchStats()
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.fetchSettlementList()
    },
    handleCurrentChange(page) {
      this.pagination.page = page
      this.fetchSettlementList()
    },
    handleSortChange({ column, prop, order }) {
      this.sortField = prop
      this.sortOrder = order === 'ascending' ? 'asc' : 'desc'
      this.fetchSettlementList()
    },
    showApplyDialog() {
      this.applyDialogVisible = true
    },
    handleApplySuccess() {
      this.applyDialogVisible = false
      this.fetchSettlementList()
      this.fetchAvailableAmount()
      this.$message.success('结算申请提交成功！')
    },
    viewDetail(row) {
      this.$router.push(`/settlement/detail/${row.settle_id}`)
    },
    getStatusText(status) {
      return SETTLEMENT_STATUS[status]?.text || '未知'
    },
    getStatusType(status) {
      return SETTLEMENT_STATUS[status]?.type || 'info'
    },
    formatMoney(amount) {
      return Number(amount || 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.settlement-list {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #333;
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  font-size: 14px;
}

.available-card {
  margin-bottom: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.available-card :deep(.el-card__body) {
  padding: 30px;
}

.available-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.available-info {
  flex: 1;
}

.available-amount {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.amount-label {
  font-size: 16px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.amount-value {
  font-size: 36px;
  font-weight: 600;
}

.available-desc p {
  margin: 4px 0;
  opacity: 0.8;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stats-item {
  text-align: center;
}

.stats-content {
  padding: 10px 0;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #666;
}

.table-card {
  margin-bottom: 20px;
}

.amount-text {
  font-weight: 600;
  color: #67C23A;
}

.fee-text {
  color: #E6A23C;
}

.actual-amount {
  font-weight: 600;
  color: #409EFF;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

@media (max-width: 768px) {
  .available-content {
    flex-direction: column;
    text-align: center;
  }
  
  .available-actions {
    margin-top: 20px;
  }
  
  .search-form .el-form-item {
    margin-bottom: 10px;
  }
  
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .pagination-wrapper {
    text-align: center;
  }
}
</style>