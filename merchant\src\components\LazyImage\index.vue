<template>
  <div class="lazy-image-container" :style="containerStyle">
    <img
      v-if="loaded"
      :src="src"
      :alt="alt"
      :class="['lazy-image', { 'fade-in': loaded }]"
      @load="onLoad"
      @error="onError"
    />
    <div v-else-if="loading" class="lazy-image-placeholder">
      <i class="el-icon-loading"></i>
      <span v-if="showLoadingText">{{ loadingText }}</span>
    </div>
    <div v-else-if="error" class="lazy-image-error">
      <i class="el-icon-picture-outline"></i>
      <span v-if="showErrorText">{{ errorText }}</span>
    </div>
    <div v-else class="lazy-image-placeholder">
      <i class="el-icon-picture-outline"></i>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LazyImage',
  props: {
    src: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      default: ''
    },
    width: {
      type: [String, Number],
      default: 'auto'
    },
    height: {
      type: [String, Number],
      default: 'auto'
    },
    placeholder: {
      type: String,
      default: ''
    },
    loadingText: {
      type: String,
      default: '加载中...'
    },
    errorText: {
      type: String,
      default: '加载失败'
    },
    showLoadingText: {
      type: Boolean,
      default: false
    },
    showErrorText: {
      type: Boolean,
      default: false
    },
    threshold: {
      type: Number,
      default: 0.1
    },
    rootMargin: {
      type: String,
      default: '50px'
    }
  },
  data() {
    return {
      loading: false,
      loaded: false,
      error: false,
      observer: null
    }
  },
  computed: {
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? `${this.width}px` : this.width,
        height: typeof this.height === 'number' ? `${this.height}px` : this.height
      }
    }
  },
  mounted() {
    this.initIntersectionObserver()
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect()
    }
  },
  methods: {
    initIntersectionObserver() {
      if ('IntersectionObserver' in window) {
        this.observer = new IntersectionObserver(
          (entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting && !this.loaded && !this.loading) {
                this.loadImage()
                this.observer.unobserve(entry.target)
              }
            })
          },
          {
            threshold: this.threshold,
            rootMargin: this.rootMargin
          }
        )
        this.observer.observe(this.$el)
      } else {
        // Fallback for browsers without IntersectionObserver
        this.loadImage()
      }
    },
    loadImage() {
      this.loading = true
      this.error = false
      
      const img = new Image()
      img.onload = () => {
        this.loading = false
        this.loaded = true
        this.$emit('load')
      }
      img.onerror = () => {
        this.loading = false
        this.error = true
        this.$emit('error')
      }
      img.src = this.src
    },
    onLoad() {
      this.$emit('loaded')
    },
    onError() {
      this.$emit('failed')
    }
  }
}
</script>

<style lang="scss" scoped>
.lazy-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
  background-color: #f5f7fa;
  
  .lazy-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.3s ease;
    
    &.fade-in {
      opacity: 1;
    }
  }
  
  .lazy-image-placeholder,
  .lazy-image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #c0c4cc;
    font-size: 14px;
    
    i {
      font-size: 24px;
      margin-bottom: 8px;
    }
    
    &.lazy-image-error {
      color: #f56c6c;
    }
  }
  
  .lazy-image-placeholder {
    i.el-icon-loading {
      animation: rotating 2s linear infinite;
    }
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>