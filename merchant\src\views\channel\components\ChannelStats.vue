<template>
  <div class="channel-stats">
    <div class="stats-header">
      <h3>{{ channelInfo.channel_name }} 统计数据</h3>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        @change="fetchStats"
        style="margin-left: 20px"
      ></el-date-picker>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-card class="stats-card">
        <div class="stats-content">
          <div class="stats-value">{{ stats.totalOrders }}</div>
          <div class="stats-label">总订单数</div>
        </div>
        <div class="stats-icon">
          <i class="el-icon-document"></i>
        </div>
      </el-card>
      
      <el-card class="stats-card">
        <div class="stats-content">
          <div class="stats-value">¥{{ formatMoney(stats.totalAmount) }}</div>
          <div class="stats-label">总交易金额</div>
        </div>
        <div class="stats-icon">
          <i class="el-icon-money"></i>
        </div>
      </el-card>
      
      <el-card class="stats-card">
        <div class="stats-content">
          <div class="stats-value">{{ stats.successOrders }}</div>
          <div class="stats-label">成功订单</div>
        </div>
        <div class="stats-icon">
          <i class="el-icon-success"></i>
        </div>
      </el-card>
      
      <el-card class="stats-card">
        <div class="stats-content">
          <div class="stats-value">{{ (stats.successRate * 100).toFixed(1) }}%</div>
          <div class="stats-label">成功率</div>
        </div>
        <div class="stats-icon">
          <i class="el-icon-data-line"></i>
        </div>
      </el-card>
    </div>
    
    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>交易趋势</span>
            </div>
            <div id="trendChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>成功率趋势</span>
            </div>
            <div id="successRateChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 详细数据表格 -->
    <el-card class="table-card">
      <div slot="header">
        <span>每日统计</span>
      </div>
      <el-table
        v-loading="loading"
        :data="dailyStats"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="date" label="日期" width="120"></el-table-column>
        <el-table-column prop="orders" label="订单数" width="100"></el-table-column>
        <el-table-column prop="amount" label="交易金额" width="150">
          <template slot-scope="scope">
            ¥{{ formatMoney(scope.row.amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="success_orders" label="成功订单" width="100"></el-table-column>
        <el-table-column prop="success_rate" label="成功率" width="100">
          <template slot-scope="scope">
            <span :class="getSuccessRateClass(scope.row.success_rate)">
              {{ (scope.row.success_rate * 100).toFixed(1) }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="avg_amount" label="平均金额" width="120">
          <template slot-scope="scope">
            ¥{{ formatMoney(scope.row.avg_amount) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getChannelStats } from '@/api/channel'

export default {
  name: 'ChannelStats',
  props: {
    channelInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      dateRange: [],
      stats: {
        totalOrders: 0,
        totalAmount: 0,
        successOrders: 0,
        successRate: 0
      },
      dailyStats: []
    }
  },
  watch: {
    channelInfo: {
      handler(newVal) {
        if (newVal && newVal.id) {
          this.initDateRange()
          this.fetchStats()
        }
      },
      immediate: true
    }
  },
  methods: {
    initDateRange() {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 30)
      this.dateRange = [
        start.toISOString().slice(0, 10),
        end.toISOString().slice(0, 10)
      ]
    },
    async fetchStats() {
      if (!this.channelInfo.id) return
      
      this.loading = true
      try {
        const params = {}
        if (this.dateRange && this.dateRange.length === 2) {
          params.date_start = this.dateRange[0]
          params.date_end = this.dateRange[1]
        }
        
        const response = await getChannelStats(this.channelInfo.id, params)
        if (response.code === 0) {
          this.stats = response.data.summary || {}
          this.dailyStats = response.data.daily || []
          this.$nextTick(() => {
            this.renderCharts()
          })
        } else {
          this.$message.error(response.msg || '获取统计数据失败')
        }
      } catch (error) {
        console.error('Fetch channel stats error:', error)
        this.$message.error('获取统计数据失败')
      } finally {
        this.loading = false
      }
    },
    renderCharts() {
      // 这里可以使用 ECharts 或其他图表库来渲染图表
      // 由于没有引入图表库，这里只是示例代码
      console.log('Rendering charts with data:', this.dailyStats)
    },
    getSuccessRateClass(rate) {
      if (rate >= 0.95) return 'success-rate-high'
      if (rate >= 0.8) return 'success-rate-medium'
      return 'success-rate-low'
    },
    formatMoney(amount) {
      return Number(amount || 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }
  }
}
</script>

<style scoped>
.channel-stats {
  padding: 20px 0;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stats-header h3 {
  margin: 0;
  color: #333;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.stats-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 0;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #666;
}

.stats-icon {
  font-size: 32px;
  color: #409EFF;
  margin-top: 10px;
}

.charts-section {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.success-rate-high {
  color: #67C23A;
  font-weight: 600;
}

.success-rate-medium {
  color: #E6A23C;
  font-weight: 600;
}

.success-rate-low {
  color: #F56C6C;
  font-weight: 600;
}

@media (max-width: 768px) {
  .stats-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .stats-header .el-date-picker {
    margin-left: 0;
    margin-top: 10px;
  }
  
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>