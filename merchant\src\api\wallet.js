import request from '@/utils/request'

// 获取钱包余额
export function getWalletBalance() {
  return request({
    url: '/api/wallet/balance',
    method: 'get'
  })
}

// 获取资金流水记录
export function getTransactionList(params) {
  return request({
    url: '/api/wallet/transactions',
    method: 'get',
    params: {
      page: params.page || 1,
      size: params.size || 20,
      type: params.type,
      created_at_start: params.created_at_start,
      created_at_end: params.created_at_end
    }
  })
}

// USDT充值 - 获取充值地址
export function getUsdtRechargeAddress() {
  return request({
    url: '/api/wallet/usdt/address',
    method: 'get'
  })
}

// USDT充值 - 创建充值订单
export function createUsdtRecharge(data) {
  return request({
    url: '/api/wallet/usdt/recharge',
    method: 'post',
    data: {
      amount: data.amount,
      tx_hash: data.tx_hash,
      network: data.network || 'TRC20'
    }
  })
}

// 获取USDT充值记录
export function getUsdtRechargeList(params) {
  return request({
    url: '/api/wallet/usdt/recharge-list',
    method: 'get',
    params: {
      page: params.page || 1,
      size: params.size || 20,
      status: params.status,
      created_at_start: params.created_at_start,
      created_at_end: params.created_at_end
    }
  })
}

// 获取USDT汇率
export function getUsdtRate() {
  return request({
    url: '/api/wallet/usdt/rate',
    method: 'get'
  })
}

// 交易类型枚举
export const TRANSACTION_TYPE = {
  1: { text: '充值', type: 'success', icon: 'el-icon-plus' },
  2: { text: '提现', type: 'warning', icon: 'el-icon-minus' },
  3: { text: '收入', type: 'success', icon: 'el-icon-arrow-down' },
  4: { text: '支出', type: 'danger', icon: 'el-icon-arrow-up' },
  5: { text: '手续费', type: 'info', icon: 'el-icon-money' },
  6: { text: '退款', type: 'warning', icon: 'el-icon-refresh' }
}

// USDT充值状态枚举
export const USDT_RECHARGE_STATUS = {
  0: { text: '待确认', type: 'warning' },
  1: { text: '确认中', type: 'info' },
  2: { text: '充值成功', type: 'success' },
  3: { text: '充值失败', type: 'danger' }
}

// 获取交易类型选项
export function getTransactionTypeOptions() {
  return Object.keys(TRANSACTION_TYPE).map(key => ({
    value: parseInt(key),
    label: TRANSACTION_TYPE[key].text
  }))
}

// 获取USDT充值状态选项
export function getUsdtRechargeStatusOptions() {
  return Object.keys(USDT_RECHARGE_STATUS).map(key => ({
    value: parseInt(key),
    label: USDT_RECHARGE_STATUS[key].text
  }))
}