<template>
  <div class="usdt-recharge">
    <div class="page-header">
      <h2>USDT 充值</h2>
      <p>使用 USDT 为您的账户充值</p>
    </div>
    
    <!-- 汇率信息 -->
    <el-card class="rate-card">
      <div class="rate-content">
        <div class="rate-info">
          <div class="rate-label">当前汇率</div>
          <div class="rate-value">1 USDT = ¥{{ formatMoney(usdtRate) }}</div>
          <div class="rate-desc">汇率每5分钟更新一次</div>
        </div>
        <div class="rate-icon">
          <i class="el-icon-money"></i>
        </div>
      </div>
    </el-card>
    
    <!-- 充值步骤 -->
    <el-card class="steps-card">
      <div slot="header">
        <span>充值步骤</span>
      </div>
      <el-steps :active="currentStep" align-center>
        <el-step title="选择金额" description="输入充值金额"></el-step>
        <el-step title="转账USDT" description="向指定地址转账"></el-step>
        <el-step title="提交凭证" description="提交交易哈希"></el-step>
        <el-step title="等待确认" description="等待区块链确认"></el-step>
      </el-steps>
    </el-card>
    
    <!-- 充值表单 -->
    <el-card class="form-card" v-if="currentStep === 0">
      <div slot="header">
        <span>第一步：选择充值金额</span>
      </div>
      <el-form
        ref="rechargeForm"
        :model="rechargeForm"
        :rules="rechargeRules"
        label-width="120px"
      >
        <el-form-item label="充值金额" prop="amount">
          <el-input
            v-model="rechargeForm.amount"
            placeholder="请输入USDT金额"
            type="number"
            :min="10"
          >
            <template slot="append">USDT</template>
          </el-input>
          <div class="amount-tips">
            <p>最小充值金额：10 USDT</p>
            <p v-if="rechargeForm.amount">约合人民币：¥{{ formatMoney(rechargeForm.amount * usdtRate) }}</p>
          </div>
        </el-form-item>
        
        <el-form-item label="网络类型" prop="network">
          <el-radio-group v-model="rechargeForm.network">
            <el-radio label="TRC20">TRC20 (推荐)</el-radio>
            <el-radio label="ERC20">ERC20</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="nextStep" :disabled="!rechargeForm.amount">
            下一步
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { 
  getUsdtRate, 
  getUsdtRechargeAddress, 
  createUsdtRecharge, 
  getUsdtRechargeList 
} from '@/api/wallet'

export default {
  name: 'UsdtRecharge',
  data() {
    return {
      loading: false,
      submitting: false,
      historyLoading: false,
      currentStep: 0,
      usdtRate: 7.2,
      rechargeAddress: '',
      rechargeHistory: [],
      rechargeForm: {
        amount: '',
        tx_hash: '',
        network: 'TRC20'
      }
    }
  },
  computed: {
    rechargeRules() {
      return {
        amount: [
          { required: true, message: '请输入充值金额', trigger: 'blur' },
          { type: 'number', min: 10, message: '最小充值金额为10 USDT', trigger: 'blur' }
        ],
        network: [
          { required: true, message: '请选择网络类型', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.fetchUsdtRate()
    this.fetchRechargeAddress()
    this.fetchRechargeHistory()
  },
  methods: {
    async fetchUsdtRate() {
      try {
        const response = await getUsdtRate()
        if (response.code === 0) {
          this.usdtRate = response.data.rate
        }
      } catch (error) {
        console.error('Fetch USDT rate error:', error)
      }
    },
    async fetchRechargeAddress() {
      try {
        const response = await getUsdtRechargeAddress()
        if (response.code === 0) {
          this.rechargeAddress = response.data.address
        }
      } catch (error) {
        console.error('Fetch recharge address error:', error)
      }
    },
    async fetchRechargeHistory() {
      this.historyLoading = true
      try {
        const response = await getUsdtRechargeList({ page: 1, size: 5 })
        if (response.code === 0) {
          this.rechargeHistory = response.data.list || []
        }
      } catch (error) {
        console.error('Fetch recharge history error:', error)
      } finally {
        this.historyLoading = false
      }
    },
    nextStep() {
      if (this.currentStep === 0) {
        this.$refs.rechargeForm.validate((valid) => {
          if (valid) {
            this.currentStep = 1
          }
        })
      } else if (this.currentStep < 3) {
        this.currentStep++
      }
    },
    formatMoney(amount) {
      return Number(amount || 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }
  }
}
</script>

<style scoped>
.usdt-recharge {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #333;
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  font-size: 14px;
}

.rate-card {
  margin-bottom: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.rate-card :deep(.el-card__body) {
  padding: 30px;
}

.rate-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rate-info {
  flex: 1;
}

.rate-label {
  font-size: 16px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.rate-value {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 4px;
}

.rate-desc {
  font-size: 12px;
  opacity: 0.8;
}

.rate-icon {
  font-size: 48px;
  opacity: 0.3;
}

.steps-card,
.form-card {
  margin-bottom: 20px;
}

.amount-tips {
  margin-top: 8px;
}

.amount-tips p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

@media (max-width: 768px) {
  .rate-content {
    flex-direction: column;
    text-align: center;
  }
  
  .rate-icon {
    margin-top: 16px;
  }
}
</style>