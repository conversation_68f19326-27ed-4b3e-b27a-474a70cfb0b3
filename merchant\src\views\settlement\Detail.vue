<template>
  <div class="settlement-detail">
    <div class="page-header">
      <el-button @click="goBack" icon="el-icon-arrow-left">返回</el-button>
      <div class="header-info">
        <h2>结算详情</h2>
        <p>结算单号：{{ settlementInfo.settle_id }}</p>
      </div>
    </div>
    
    <div v-loading="loading" class="detail-content">
      <!-- 结算状态 -->
      <el-card class="status-card">
        <div class="status-header">
          <div class="status-icon">
            <i :class="statusIcon" :style="{ color: statusColor }"></i>
          </div>
          <div class="status-info">
            <h3>{{ getStatusText(settlementInfo.status) }}</h3>
            <p class="status-desc">{{ getStatusDesc(settlementInfo.status) }}</p>
          </div>
        </div>
        
        <!-- 结算进度 -->
        <div class="progress-section">
          <el-steps :active="currentStep" finish-status="success" align-center>
            <el-step title="申请提交" :description="formatTime(settlementInfo.created_at)"></el-step>
            <el-step title="审核中" :description="settlementInfo.status >= 1 ? '平台审核中' : ''"></el-step>
            <el-step title="审核通过" :description="settlementInfo.status >= 2 ? '审核通过' : ''"></el-step>
            <el-step title="结算完成" :description="settlementInfo.status === 5 ? formatTime(settlementInfo.updated_at) : ''"></el-step>
          </el-steps>
        </div>
      </el-card>
      
      <!-- 基本信息 -->
      <el-card class="info-card">
        <div slot="header">
          <span>基本信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="结算单号">{{ settlementInfo.settle_id || '-' }}</el-descriptions-item>
          <el-descriptions-item label="商户号">{{ settlementInfo.mch_no || '-' }}</el-descriptions-item>
          <el-descriptions-item label="申请金额">
            <span class="amount-text">¥{{ formatMoney(settlementInfo.amount) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="手续费">
            <span class="fee-text">¥{{ formatMoney(settlementInfo.fee) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="实际到账">
            <span class="actual-amount">¥{{ formatMoney(settlementInfo.actual_amount) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="结算状态">
            <el-tag :type="getStatusType(settlementInfo.status)">
              {{ getStatusText(settlementInfo.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatTime(settlementInfo.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatTime(settlementInfo.updated_at) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 收款账户信息 -->
      <el-card class="info-card">
        <div slot="header">
          <span>收款账户信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="银行名称">{{ settlementInfo.bank_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="银行账号">{{ formatBankAccount(settlementInfo.bank_account) }}</el-descriptions-item>
          <el-descriptions-item label="账户姓名">{{ settlementInfo.account_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="开户行">{{ settlementInfo.bank_branch || '-' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 审核信息 -->
      <el-card class="info-card" v-if="settlementInfo.audit_info">
        <div slot="header">
          <span>审核信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="审核人">{{ settlementInfo.audit_info.auditor || '-' }}</el-descriptions-item>
          <el-descriptions-item label="审核时间">{{ formatTime(settlementInfo.audit_info.audit_time) }}</el-descriptions-item>
          <el-descriptions-item label="审核结果">
            <el-tag :type="settlementInfo.audit_info.result === 1 ? 'success' : 'danger'">
              {{ settlementInfo.audit_info.result === 1 ? '通过' : '拒绝' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="审核备注" span="2">
            {{ settlementInfo.audit_info.remark || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 结算信息 -->
      <el-card class="info-card" v-if="settlementInfo.settle_info">
        <div slot="header">
          <span>结算信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="结算渠道">{{ settlementInfo.settle_info.channel || '-' }}</el-descriptions-item>
          <el-descriptions-item label="结算流水号">{{ settlementInfo.settle_info.transaction_id || '-' }}</el-descriptions-item>
          <el-descriptions-item label="结算时间">{{ formatTime(settlementInfo.settle_info.settle_time) }}</el-descriptions-item>
          <el-descriptions-item label="结算状态">
            <el-tag :type="settlementInfo.settle_info.status === 1 ? 'success' : 'danger'">
              {{ settlementInfo.settle_info.status === 1 ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 备注信息 -->
      <el-card class="info-card" v-if="settlementInfo.remark">
        <div slot="header">
          <span>备注信息</span>
        </div>
        <div class="remark-content">
          {{ settlementInfo.remark }}
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getSettlementDetail, SETTLEMENT_STATUS } from '@/api/settlement'

export default {
  name: 'SettlementDetail',
  data() {
    return {
      loading: false,
      settlementInfo: {}
    }
  },
  computed: {
    settleId() {
      return this.$route.params.id
    },
    statusIcon() {
      const iconMap = {
        0: 'el-icon-time',
        1: 'el-icon-loading',
        2: 'el-icon-success',
        3: 'el-icon-error',
        4: 'el-icon-loading',
        5: 'el-icon-success',
        6: 'el-icon-error'
      }
      return iconMap[this.settlementInfo.status] || 'el-icon-question'
    },
    statusColor() {
      const colorMap = {
        0: '#E6A23C',
        1: '#409EFF',
        2: '#67C23A',
        3: '#F56C6C',
        4: '#409EFF',
        5: '#67C23A',
        6: '#F56C6C'
      }
      return colorMap[this.settlementInfo.status] || '#909399'
    },
    currentStep() {
      const stepMap = {
        0: 1, // 待审核
        1: 2, // 审核中
        2: 3, // 审核通过
        3: 2, // 审核拒绝
        4: 3, // 结算中
        5: 4, // 结算完成
        6: 3  // 结算失败
      }
      return stepMap[this.settlementInfo.status] || 0
    }
  },
  created() {
    this.fetchSettlementDetail()
  },
  methods: {
    async fetchSettlementDetail() {
      this.loading = true
      try {
        const response = await getSettlementDetail(this.settleId)
        if (response.code === 0) {
          this.settlementInfo = response.data
        } else {
          this.$message.error(response.msg || '获取结算详情失败')
        }
      } catch (error) {
        console.error('Fetch settlement detail error:', error)
        this.$message.error('获取结算详情失败')
      } finally {
        this.loading = false
      }
    },
    goBack() {
      this.$router.go(-1)
    },
    getStatusText(status) {
      return SETTLEMENT_STATUS[status]?.text || '未知'
    },
    getStatusType(status) {
      return SETTLEMENT_STATUS[status]?.type || 'info'
    },
    getStatusDesc(status) {
      const descMap = {
        0: '结算申请已提交，等待平台审核',
        1: '平台正在审核您的结算申请',
        2: '审核通过，等待结算处理',
        3: '审核未通过，请查看审核备注',
        4: '结算处理中，请耐心等待',
        5: '结算已完成，资金已到账',
        6: '结算失败，请联系客服'
      }
      return descMap[status] || ''
    },
    formatBankAccount(account) {
      if (!account) return '-'
      // 银行账号脱敏处理
      if (account.length > 8) {
        return account.substring(0, 4) + '****' + account.substring(account.length - 4)
      }
      return account
    },
    formatMoney(amount) {
      return Number(amount || 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.settlement-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.header-info {
  margin-left: 16px;
}

.header-info h2 {
  color: #333;
  margin: 0 0 4px 0;
}

.header-info p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.status-card {
  margin-bottom: 20px;
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-icon {
  margin-right: 20px;
}

.status-icon i {
  font-size: 48px;
}

.status-info h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 20px;
}

.status-desc {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.progress-section {
  padding: 20px 0;
}

.info-card {
  margin-bottom: 20px;
}

.amount-text {
  font-weight: 600;
  color: #67C23A;
  font-size: 16px;
}

.fee-text {
  color: #E6A23C;
  font-weight: 600;
}

.actual-amount {
  font-weight: 600;
  color: #409EFF;
  font-size: 16px;
}

.remark-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
  color: #333;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-info {
    margin-left: 0;
    margin-top: 10px;
  }
  
  .status-header {
    flex-direction: column;
    text-align: center;
  }
  
  .status-icon {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style>