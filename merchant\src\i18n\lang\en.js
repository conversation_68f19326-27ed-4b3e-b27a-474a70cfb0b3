export default {
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    reset: 'Reset',
    submit: 'Submit',
    back: 'Back',
    loading: 'Loading...',
    noData: 'No Data',
    operation: 'Operation',
    status: 'Status',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    total: 'Total',
    items: 'items',
    page: 'Page',
    export: 'Export',
    import: 'Import',
    refresh: 'Refresh',
    view: 'View',
    detail: 'Detail',
    language: 'Language',
    chinese: '中文',
    english: 'English'
  },
  login: {
    title: 'Merchant Management System',
    username: 'Username',
    password: 'Password',
    login: 'Login',
    loginSuccess: 'Login Success',
    loginFailed: 'Login Failed',
    usernameError: 'Please enter correct username',
    passwordError: 'Password must be at least 6 characters',
    logout: 'Logout',
    logoutConfirm: 'Are you sure to logout?'
  },
  menu: {
    dashboard: 'Dashboard',
    merchant: 'Merchant Management',
    merchantRegister: 'Merchant Register',
    kycStatus: 'KYC Status',
    order: 'Order Management',
    orderList: 'Order List',
    settlement: 'Settlement Management',
    settlementList: 'Settlement List',
    channel: 'Channel Configuration',
    channelList: 'Channel List',
    wallet: 'Wallet Management',
    walletBalance: 'Balance Inquiry',
    usdtRecharge: 'USDT Recharge'
  },
  merchant: {
    merchantNo: 'Merchant No',
    merchantName: 'Merchant Name',
    contactName: 'Contact Name',
    contactPhone: 'Contact Phone',
    email: 'Email',
    address: 'Address',
    status: 'Status',
    createTime: 'Create Time',
    register: 'Merchant Register',
    registerSuccess: 'Register Success',
    registerFailed: 'Register Failed',
    type: 'Merchant Type',
    state: 'Audit Status',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected'
  },
  kyc: {
    title: 'KYC Verification',
    status: 'Verification Status',
    upload: 'Upload File',
    idCard: 'ID Card',
    businessLicense: 'Business License',
    uploadSuccess: 'Upload Success',
    uploadFailed: 'Upload Failed',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected'
  },
  order: {
    orderId: 'Order ID',
    merchantNo: 'Merchant No',
    amount: 'Order Amount',
    currency: 'Currency',
    status: 'Order Status',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    orderList: 'Order List',
    orderDetail: 'Order Detail',
    export: 'Export Orders',
    search: 'Search Orders'
  },
  settlement: {
    settleId: 'Settlement ID',
    merchantNo: 'Merchant No',
    amount: 'Settlement Amount',
    status: 'Settlement Status',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    apply: 'Apply Settlement',
    applySuccess: 'Apply Success',
    applyFailed: 'Apply Failed',
    pending: 'Pending',
    processing: 'Processing',
    completed: 'Completed',
    failed: 'Failed'
  },
  channel: {
    channelId: 'Channel ID',
    channelName: 'Channel Name',
    channelType: 'Channel Type',
    status: 'Channel Status',
    config: 'Channel Config',
    test: 'Test Connection',
    testSuccess: 'Test Success',
    testFailed: 'Test Failed',
    enabled: 'Enabled',
    disabled: 'Disabled'
  },
  wallet: {
    balance: 'Balance',
    availableBalance: 'Available Balance',
    frozenBalance: 'Frozen Balance',
    recharge: 'Recharge',
    rechargeAmount: 'Recharge Amount',
    rechargeSuccess: 'Recharge Success',
    rechargeFailed: 'Recharge Failed',
    usdtAddress: 'USDT Address',
    transactionRecord: 'Transaction Record',
    rechargeRecord: 'Recharge Record'
  },
  error: {
    networkError: 'Network connection failed, please check network settings',
    authError: 'Authentication failed, please login again',
    permissionError: 'Insufficient permissions, access denied',
    validationError: 'Request parameter error',
    businessError: 'Business processing failed',
    systemError: 'System error, please try again later',
    sessionExpired: 'Session expired, please login again',
    reLogin: 'Login Again'
  },
  export: {
    currentPage: 'Export Current Page',
    allData: 'Export All Data',
    selectedData: 'Export Selected Data',
    customExport: 'Custom Export',
    filename: 'Filename',
    filenamePlaceholder: 'Please enter filename',
    columns: 'Export Columns',
    dateRange: 'Date Range',
    exportSuccess: 'Export Success',
    exportFailed: 'Export Failed',
    noData: 'No data to export',
    selectColumns: 'Please select columns to export',
    startDate: 'Start Date',
    endDate: 'End Date',
    to: 'to'
  }
}