# Jeepay 前端逆向工程 - 功能模块与API对照表

## 分析概述

基于对后端代码和SQL文件的分析，识别出以下核心功能模块和对应的API接口。

## 功能模块与API对照表

### 1. 商户管理模块 (Merchant Management)

**Controller:** `MchInfoController`  
**路由前缀:** `/api/mchInfo`  
**主要数据表:** `t_mch_info`, `t_card_and_mch`, `t_sys_user`

| API路径 | HTTP方法 | 功能描述 | 权限 | 主要字段 |
|---------|----------|----------|------|----------|
| `/api/mchInfo` | GET | 商户信息列表 | ENT_MCH_LIST | mchNo, mchName, isvNo, type, state |
| `/api/mchInfo` | POST | 新增商户信息 | ENT_MCH_INFO_ADD | mchInfo对象, loginUserName |
| `/api/mchInfo/{mchNo}` | GET | 查询商户详情 | ENT_MCH_INFO_VIEW | mchNo |
| `/api/mchInfo/{mchNo}` | PUT | 更新商户信息 | ENT_MCH_INFO_EDIT | mchInfo对象, resetPass, confirmPwd |
| `/api/mchInfo/{mchNo}` | DELETE | 删除商户信息 | ENT_MCH_INFO_DEL | mchNo |
| `/api/mchInfo/getWhiteIp/{mchNo}` | GET | 获取IP白名单 | ENT_MCH_INFO_EDIT | mchNo |
| `/api/mchInfo/whiteIp/{mchNo}` | PUT | 设置IP白名单 | ENT_MCH_INFO_EDIT | apiWhiteIp, loginWhiteIp |
| `/api/mchInfo/resetGoogleVertif/{mchNo}` | PUT | 重置Google验证码 | ENT_MCH_INFO_EDIT | mchNo |

**关键数据字段:**
- `mch_no` (商户号) - 主键
- `mch_name` (商户名称)
- `mch_short_name` (商户简称)
- `type` (商户类型: 1-普通商户, 2-特约商户)
- `state` (状态: 0-停用, 1-正常)
- `contact_name` (联系人姓名)
- `contact_tel` (联系人手机号)
- `google_private` (Google验证码密钥)
- `rae_type_exchange` (支付费率值)
- `tran_accounts_exchange` (转账费率值)

### 2. 商户应用管理模块 (Merchant App Management)

**Controller:** `MchAppController`  
**路由前缀:** `/api/mchApps`  
**主要数据表:** `t_mch_app`

| API路径 | HTTP方法 | 功能描述 | 权限 | 主要字段 |
|---------|----------|----------|------|----------|
| `/api/mchApps` | GET | 应用列表 | ENT_MCH_APP_LIST | mchApp对象 |
| `/api/mchApps` | POST | 新建应用 | ENT_MCH_APP_ADD | mchApp对象 |
| `/api/mchApps/{appId}` | GET | 应用详情 | ENT_MCH_APP_VIEW | appId |
| `/api/mchApps/{appId}` | PUT | 更新应用信息 | ENT_MCH_APP_EDIT | mchApp对象 |
| `/api/mchApps/{appId}` | DELETE | 删除应用 | ENT_MCH_APP_DEL | appId |

**关键数据字段:**
- `app_id` (应用ID) - 主键
- `app_name` (应用名称)
- `mch_no` (商户号)
- `app_secret` (应用密钥)
- `state` (状态)

### 3. 支付订单管理模块 (Payment Order Management)

**Controller:** `PayOrderController`  
**路由前缀:** `/api/payOrder`  
**主要数据表:** `t_pay_order`, `t_h5account_number`, `t_bank_account_deal`

| API路径 | HTTP方法 | 功能描述 | 权限 | 主要字段 |
|---------|----------|----------|------|----------|
| `/api/payOrder` | GET | 订单信息列表 | ENT_ORDER_LIST | payOrder对象, 分页参数 |
| `/api/payOrder/account` | GET | 收款账户订单列表 | ENT_ORDER_LIST | payOrder对象 |
| `/api/payOrder/export` | GET | 导出订单数据 | ENT_PAY_ORDER_EXPORT | 查询条件 |
| `/api/payOrder/{payOrderId}` | GET | 支付订单详情 | ENT_PAY_ORDER_VIEW | payOrderId |
| `/api/payOrder/refunds/{payOrderId}` | POST | 发起订单退款 | ENT_PAY_ORDER_REFUND | refundAmount, refundReason |
| `/api/payOrder/queryManualastate` | GET | 查询手动修改状态权限 | ENT_PAY_ORDER_VIEW | - |
| `/api/payOrder/manualAlterOrderState` | PUT | 手动修改订单状态 | ENT_PAY_ORDER_VIEW | ManualAlterOrderStateDTO |

**关键数据字段:**
- `pay_order_id` (支付订单号) - 主键
- `mch_no` (商户号)
- `card_no` (卡商号)
- `app_id` (应用ID)
- `amount` (支付金额)
- `practical_amount` (实际支付金额)
- `state` (支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败)
- `pay_type` (支付类型: 1-H5, 2-插件, 3-二维码, 4-API, 5-收款账户)
- `mch_fee_amount` (商户手续费)
- `currency` (货币代码)
- `subject` (商品标题)
- `created_at` (创建时间)

### 4. 退款订单管理模块 (Refund Order Management)

**Controller:** `RefundOrderController`  
**路由前缀:** `/api/refundOrder`  
**主要数据表:** `t_refund_order`

| API路径 | HTTP方法 | 功能描述 | 权限 | 主要字段 |
|---------|----------|----------|------|----------|
| `/api/refundOrder` | GET | 退款订单列表 | ENT_REFUND_LIST | refundOrder对象 |
| `/api/refundOrder/{refundOrderId}` | GET | 退款订单详情 | ENT_REFUND_ORDER_VIEW | refundOrderId |

**关键数据字段:**
- `refund_order_id` (退款订单号) - 主键
- `pay_order_id` (支付订单号)
- `mch_no` (商户号)
- `refund_amount` (退款金额)
- `state` (退款状态)

### 5. 系统用户管理模块 (System User Management)

**Controller:** `SysUserController`  
**路由前缀:** `/api/sysUsers`  
**主要数据表:** `t_sys_user`, `t_sys_user_auth`

| API路径 | HTTP方法 | 功能描述 | 权限 | 主要字段 |
|---------|----------|----------|------|----------|
| `/api/sysUsers` | GET | 操作员列表 | ENT_UR_USER_LIST | realname, sysUserId |
| `/api/sysUsers` | POST | 添加管理员 | ENT_UR_USER_ADD | sysUser对象 |
| `/api/sysUsers/{recordId}` | GET | 操作员详情 | ENT_UR_USER_EDIT | recordId |
| `/api/sysUsers/{recordId}` | PUT | 修改操作员信息 | ENT_UR_USER_EDIT | sysUser对象, resetPass |
| `/api/sysUsers/{recordId}` | DELETE | 删除操作员 | ENT_UR_USER_DELETE | recordId |
| `/api/sysUsers/listACardDealer` | GET | 获取卡商列表 | - | - |

**关键数据字段:**
- `sys_user_id` (系统用户ID) - 主键
- `login_username` (登录用户名)
- `realname` (真实姓名)
- `telphone` (手机号)
- `state` (状态: 0-停用, 1-启用)
- `sys_type` (系统类型: MGR-运营平台, MCH-商户中心)
- `google_private` (Google验证码密钥)
- `belong_info_id` (所属商户ID)

### 6. 银行管理模块 (Bank Management)

**主要数据表:** `bank_number`, `t_bank`, `t_bank_account`

**关键数据字段:**
- `bank_number` 表: 银行代码映射表
  - `bank_name` (银行名称)
  - `bank_number` (银行代码)
- `t_bank_account` 表: 银行账户信息
  - `bank_account_id` (银行卡编号)
  - `bank_card_no` (银行卡号)

### 7. 卡商管理模块 (Card Dealer Management)

**主要数据表:** `t_card_info`, `t_card_and_mch`

**关键数据字段:**
- `card_no` (卡商号) - 主键
- `card_name` (卡商名称)

### 8. 系统配置模块 (System Configuration)

**主要数据表:** `t_sys_config`, `t_sys_role`, `t_sys_entitlement`

**关键数据字段:**
- 系统配置、角色权限、权限管理等

## 前端页面结构推导

基于API分析，推导出以下前端页面结构：

### 1. 商户管理页面
- **列表页面:** `src/views/merchant/MerchantList.vue`
- **新增/编辑页面:** `src/views/merchant/MerchantForm.vue`
- **详情页面:** `src/views/merchant/MerchantDetail.vue`
- **API服务:** `src/api/merchant.js`

### 2. 商户应用管理页面
- **列表页面:** `src/views/merchant/AppList.vue`
- **新增/编辑页面:** `src/views/merchant/AppForm.vue`
- **API服务:** `src/api/merchantApp.js`

### 3. 支付订单管理页面
- **列表页面:** `src/views/order/PayOrderList.vue`
- **详情页面:** `src/views/order/PayOrderDetail.vue`
- **收款账户订单页面:** `src/views/order/AccountOrderList.vue`
- **API服务:** `src/api/payOrder.js`

### 4. 退款订单管理页面
- **列表页面:** `src/views/order/RefundOrderList.vue`
- **详情页面:** `src/views/order/RefundOrderDetail.vue`
- **API服务:** `src/api/refundOrder.js`

### 5. 系统用户管理页面
- **列表页面:** `src/views/system/UserList.vue`
- **新增/编辑页面:** `src/views/system/UserForm.vue`
- **API服务:** `src/api/sysUser.js`

## 技术特点分析

### 1. 权限控制
- 使用 `@PreAuthorize` 注解进行权限控制
- 权限码格式: `ENT_模块_操作` (如: ENT_MCH_LIST, ENT_MCH_INFO_ADD)

### 2. 数据处理
- 使用 MyBatis Plus 进行数据库操作
- 支持分页查询 (`IPage`)
- 金额单位统一为分 (需要前端转换为元显示)

### 3. 业务特色
- 支持Google验证码二次验证
- 支持IP白名单限制
- 支持多种支付方式和支付类型
- 支持费率配置和手续费计算
- 支持卡商绑定和管理

### 4. 前端技术栈要求
- Vue 2 + Element UI
- Axios 进行API调用
- Vue Router 进行路由管理
- Vuex 进行状态管理

## 下一步计划

1. 确认功能模块划分和API映射关系
2. 按模块逐个生成Vue前端代码
3. 优先实现核心模块：商户管理、支付订单管理
4. 确保生成的代码符合Jeepay前端规范