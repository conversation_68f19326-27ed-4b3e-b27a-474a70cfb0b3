import { Message, MessageBox } from 'element-ui'
import i18n from '@/i18n'
import router from '@/router'

/**
 * 错误类型枚举
 */
export const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  BUSINESS_ERROR: 'BUSINESS_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  SYSTEM_ERROR: 'SYSTEM_ERROR'
}

/**
 * 错误码映射
 */
const ERROR_CODE_MAP = {
  400: ERROR_TYPES.VALIDATION_ERROR,
  401: ERROR_TYPES.AUTH_ERROR,
  403: ERROR_TYPES.PERMISSION_ERROR,
  404: ERROR_TYPES.BUSINESS_ERROR,
  500: ERROR_TYPES.SYSTEM_ERROR,
  502: ERROR_TYPES.NETWORK_ERROR,
  503: ERROR_TYPES.NETWORK_ERROR,
  504: ERROR_TYPES.NETWORK_ERROR
}

/**
 * 全局错误处理器
 */
class ErrorHandler {
  constructor() {
    this.setupGlobalErrorHandler()
    this.setupUnhandledRejectionHandler()
  }

  /**
   * 设置全局错误处理
   */
  setupGlobalErrorHandler() {
    window.addEventListener('error', (event) => {
      console.error('Global Error:', event.error)
      this.logError({
        type: ERROR_TYPES.SYSTEM_ERROR,
        message: event.error?.message || 'Unknown error',
        stack: event.error?.stack,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      })
    })
  }

  /**
   * 设置未处理的Promise拒绝处理
   */
  setupUnhandledRejectionHandler() {
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled Promise Rejection:', event.reason)
      this.logError({
        type: ERROR_TYPES.SYSTEM_ERROR,
        message: event.reason?.message || 'Unhandled promise rejection',
        stack: event.reason?.stack
      })
      event.preventDefault()
    })
  }

  /**
   * 处理HTTP错误
   * @param {object} error 错误对象
   */
  handleHttpError(error) {
    const { response, request, message } = error

    if (response) {
      // 服务器响应错误
      const { status, data } = response
      const errorType = ERROR_CODE_MAP[status] || ERROR_TYPES.SYSTEM_ERROR
      
      this.logError({
        type: errorType,
        status,
        message: data?.message || message,
        url: response.config?.url,
        method: response.config?.method
      })

      this.showErrorMessage(errorType, status, data?.message || message)
      this.handleSpecialErrors(status)
    } else if (request) {
      // 网络错误
      this.logError({
        type: ERROR_TYPES.NETWORK_ERROR,
        message: 'Network error',
        url: request.responseURL
      })
      
      this.showErrorMessage(ERROR_TYPES.NETWORK_ERROR)
    } else {
      // 其他错误
      this.logError({
        type: ERROR_TYPES.SYSTEM_ERROR,
        message: message || 'Unknown error'
      })
      
      this.showErrorMessage(ERROR_TYPES.SYSTEM_ERROR, null, message)
    }
  }

  /**
   * 处理业务错误
   * @param {object} error 业务错误对象
   */
  handleBusinessError(error) {
    const { code, message, data } = error
    
    this.logError({
      type: ERROR_TYPES.BUSINESS_ERROR,
      code,
      message,
      data
    })

    Message.error(message || i18n.t('common.error'))
  }

  /**
   * 显示错误消息
   * @param {string} errorType 错误类型
   * @param {number} status HTTP状态码
   * @param {string} message 错误消息
   */
  showErrorMessage(errorType, status = null, message = null) {
    let errorMessage = ''

    switch (errorType) {
      case ERROR_TYPES.NETWORK_ERROR:
        errorMessage = i18n.t('error.networkError') || '网络连接失败，请检查网络设置'
        break
      case ERROR_TYPES.AUTH_ERROR:
        errorMessage = i18n.t('error.authError') || '认证失败，请重新登录'
        break
      case ERROR_TYPES.PERMISSION_ERROR:
        errorMessage = i18n.t('error.permissionError') || '权限不足，无法访问'
        break
      case ERROR_TYPES.VALIDATION_ERROR:
        errorMessage = message || i18n.t('error.validationError') || '请求参数错误'
        break
      case ERROR_TYPES.BUSINESS_ERROR:
        errorMessage = message || i18n.t('error.businessError') || '业务处理失败'
        break
      case ERROR_TYPES.SYSTEM_ERROR:
      default:
        errorMessage = i18n.t('error.systemError') || '系统错误，请稍后重试'
        break
    }

    Message.error(errorMessage)
  }

  /**
   * 处理特殊错误状态
   * @param {number} status HTTP状态码
   */
  handleSpecialErrors(status) {
    switch (status) {
      case 401:
        // 清除认证信息并跳转到登录页
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        
        MessageBox.confirm(
          i18n.t('login.sessionExpired') || '登录已过期，请重新登录',
          i18n.t('common.warning') || '警告',
          {
            confirmButtonText: i18n.t('login.reLogin') || '重新登录',
            cancelButtonText: i18n.t('common.cancel') || '取消',
            type: 'warning'
          }
        ).then(() => {
          router.push('/login')
        }).catch(() => {
          router.push('/login')
        })
        break
      case 403:
        // 权限不足，可以跳转到403页面或显示提示
        setTimeout(() => {
          router.push('/403')
        }, 1500)
        break
    }
  }

  /**
   * 记录错误日志
   * @param {object} errorInfo 错误信息
   */
  logError(errorInfo) {
    const logData = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getCurrentUserId(),
      ...errorInfo
    }

    // 控制台输出
    console.error('Error Log:', logData)

    // 发送到服务器（可选）
    this.sendErrorToServer(logData)

    // 本地存储（用于调试）
    this.saveErrorToLocal(logData)
  }

  /**
   * 发送错误到服务器
   * @param {object} errorData 错误数据
   */
  async sendErrorToServer(errorData) {
    try {
      // 这里可以调用API发送错误日志到服务器
      // await api.post('/api/error-log', errorData)
    } catch (error) {
      console.error('Failed to send error to server:', error)
    }
  }

  /**
   * 保存错误到本地存储
   * @param {object} errorData 错误数据
   */
  saveErrorToLocal(errorData) {
    try {
      const errors = JSON.parse(localStorage.getItem('errorLogs') || '[]')
      errors.push(errorData)
      
      // 只保留最近100条错误日志
      if (errors.length > 100) {
        errors.splice(0, errors.length - 100)
      }
      
      localStorage.setItem('errorLogs', JSON.stringify(errors))
    } catch (error) {
      console.error('Failed to save error to local storage:', error)
    }
  }

  /**
   * 获取当前用户ID
   * @returns {string|null} 用户ID
   */
  getCurrentUserId() {
    try {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
      return userInfo.id || null
    } catch {
      return null
    }
  }

  /**
   * 获取本地错误日志
   * @returns {Array} 错误日志列表
   */
  getLocalErrorLogs() {
    try {
      return JSON.parse(localStorage.getItem('errorLogs') || '[]')
    } catch {
      return []
    }
  }

  /**
   * 清除本地错误日志
   */
  clearLocalErrorLogs() {
    localStorage.removeItem('errorLogs')
  }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler()

export default errorHandler