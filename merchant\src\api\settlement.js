import request from '@/utils/request'

// 获取结算记录列表
export function getSettlementList(params) {
  return request({
    url: '/api/settlement/list',
    method: 'get',
    params: {
      page: params.page || 1,
      size: params.size || 20,
      settle_id: params.settle_id,
      status: params.status,
      created_at_start: params.created_at_start,
      created_at_end: params.created_at_end,
      mch_no: params.mch_no
    }
  })
}

// 获取结算详情
export function getSettlementDetail(settleId) {
  return request({
    url: `/api/settlement/detail/${settleId}`,
    method: 'get'
  })
}

// 申请结算
export function applySettlement(data) {
  return request({
    url: '/api/settlement/apply',
    method: 'post',
    data: {
      amount: data.amount,
      bank_account: data.bank_account,
      bank_name: data.bank_name,
      account_name: data.account_name,
      remark: data.remark
    }
  })
}

// 获取结算统计
export function getSettlementStats(params) {
  return request({
    url: '/api/settlement/stats',
    method: 'get',
    params: {
      date_start: params.date_start,
      date_end: params.date_end
    }
  })
}

// 获取可结算金额
export function getAvailableAmount() {
  return request({
    url: '/api/settlement/available-amount',
    method: 'get'
  })
}

// 结算状态枚举
export const SETTLEMENT_STATUS = {
  0: { text: '待审核', type: 'warning' },
  1: { text: '审核中', type: 'info' },
  2: { text: '审核通过', type: 'success' },
  3: { text: '审核拒绝', type: 'danger' },
  4: { text: '结算中', type: 'info' },
  5: { text: '结算完成', type: 'success' },
  6: { text: '结算失败', type: 'danger' }
}

// 获取结算状态选项
export function getSettlementStatusOptions() {
  return Object.keys(SETTLEMENT_STATUS).map(key => ({
    value: parseInt(key),
    label: SETTLEMENT_STATUS[key].text
  }))
}