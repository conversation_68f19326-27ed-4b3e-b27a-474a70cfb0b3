<template>
  <div class="settlement-apply">
    <el-form
      ref="applyForm"
      :model="applyForm"
      :rules="applyRules"
      label-width="120px"
    >
      <div class="amount-section">
        <h4>结算金额</h4>
        <div class="amount-info">
          <div class="available-amount">
            <span class="label">可结算金额：</span>
            <span class="value">¥{{ formatMoney(availableAmount) }}</span>
          </div>
          <el-form-item label="申请金额" prop="amount">
            <el-input
              v-model="applyForm.amount"
              placeholder="请输入结算金额"
              type="number"
              :min="100"
              :max="availableAmount"
            >
              <template slot="prepend">¥</template>
            </el-input>
            <div class="amount-tips">
              <p>最小结算金额：¥100.00</p>
              <p>手续费率：2%</p>
              <p v-if="applyForm.amount">手续费：¥{{ formatMoney(fee) }}</p>
              <p v-if="applyForm.amount" class="actual-amount">实际到账：¥{{ formatMoney(actualAmount) }}</p>
            </div>
          </el-form-item>
        </div>
      </div>
      
      <div class="bank-section">
        <h4>收款账户</h4>
        <el-form-item label="银行名称" prop="bank_name">
          <el-select
            v-model="applyForm.bank_name"
            placeholder="请选择银行"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="bank in bankList"
              :key="bank.code"
              :label="bank.name"
              :value="bank.name"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="银行账号" prop="bank_account">
          <el-input
            v-model="applyForm.bank_account"
            placeholder="请输入银行账号"
            maxlength="30"
            show-word-limit
          ></el-input>
        </el-form-item>
        
        <el-form-item label="账户姓名" prop="account_name">
          <el-input
            v-model="applyForm.account_name"
            placeholder="请输入账户姓名"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
      </div>
      
      <el-form-item label="备注信息" prop="remark">
        <el-input
          v-model="applyForm.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
          maxlength="200"
          show-word-limit
        ></el-input>
      </el-form-item>
      
      <div class="notice-section">
        <el-alert
          title="结算须知"
          type="info"
          :closable="false"
          show-icon
        >
          <div slot="description">
            <p>1. 结算申请提交后，将在1-3个工作日内完成审核</p>
            <p>2. 审核通过后，资金将在1-2个工作日内到账</p>
            <p>3. 请确保银行账户信息准确无误，错误信息可能导致结算失败</p>
            <p>4. 结算手续费为2%，将从结算金额中扣除</p>
          </div>
        </el-alert>
      </div>
      
      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ submitting ? '提交中...' : '提交申请' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { applySettlement } from '@/api/settlement'

export default {
  name: 'SettlementApply',
  props: {
    availableAmount: {
      type: Number,
      default: 0
    }
  },
  data() {
    // 金额验证
    const validateAmount = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入结算金额'))
      } else if (value < 100) {
        callback(new Error('最小结算金额为100元'))
      } else if (value > this.availableAmount) {
        callback(new Error('结算金额不能超过可结算金额'))
      } else {
        callback()
      }
    }

    // 银行账号验证
    const validateBankAccount = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入银行账号'))
      } else if (!/^\d{10,30}$/.test(value)) {
        callback(new Error('请输入正确的银行账号'))
      } else {
        callback()
      }
    }

    return {
      submitting: false,
      applyForm: {
        amount: '',
        bank_name: '',
        bank_account: '',
        account_name: '',
        remark: ''
      },
      applyRules: {
        amount: [
          { required: true, validator: validateAmount, trigger: 'blur' }
        ],
        bank_name: [
          { required: true, message: '请选择银行', trigger: 'change' }
        ],
        bank_account: [
          { required: true, validator: validateBankAccount, trigger: 'blur' }
        ],
        account_name: [
          { required: true, message: '请输入账户姓名', trigger: 'blur' },
          { min: 2, max: 50, message: '账户姓名长度在 2 到 50 个字符', trigger: 'blur' }
        ]
      },
      bankList: [
        { code: 'ICBC', name: '中国工商银行' },
        { code: 'ABC', name: '中国农业银行' },
        { code: 'BOC', name: '中国银行' },
        { code: 'CCB', name: '中国建设银行' },
        { code: 'COMM', name: '交通银行' },
        { code: 'CMB', name: '招商银行' },
        { code: 'CMBC', name: '中国民生银行' },
        { code: 'CIB', name: '兴业银行' },
        { code: 'SPDB', name: '浦发银行' },
        { code: 'CEB', name: '光大银行' },
        { code: 'CITIC', name: '中信银行' },
        { code: 'HXB', name: '华夏银行' },
        { code: 'PSBC', name: '中国邮政储蓄银行' },
        { code: 'PAB', name: '平安银行' }
      ]
    }
  },
  computed: {
    fee() {
      const amount = parseFloat(this.applyForm.amount) || 0
      return amount * 0.02 // 2% 手续费
    },
    actualAmount() {
      const amount = parseFloat(this.applyForm.amount) || 0
      return amount - this.fee
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.applyForm.validate(async (valid) => {
        if (!valid) return

        this.submitting = true
        try {
          const response = await applySettlement({
            ...this.applyForm,
            amount: parseFloat(this.applyForm.amount)
          })
          if (response.code === 0) {
            this.$emit('success')
            this.resetForm()
          } else {
            this.$message.error(response.msg || '申请失败')
          }
        } catch (error) {
          console.error('Apply settlement error:', error)
          this.$message.error('申请失败，请稍后重试')
        } finally {
          this.submitting = false
        }
      })
    },
    handleCancel() {
      this.$emit('cancel')
      this.resetForm()
    },
    resetForm() {
      this.applyForm = {
        amount: '',
        bank_name: '',
        bank_account: '',
        account_name: '',
        remark: ''
      }
      this.$refs.applyForm && this.$refs.applyForm.resetFields()
    },
    formatMoney(amount) {
      return Number(amount || 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }
  }
}
</script>

<style scoped>
.settlement-apply {
  padding: 20px 0;
}

.amount-section,
.bank-section {
  margin-bottom: 30px;
}

.amount-section h4,
.bank-section h4 {
  margin-bottom: 16px;
  color: #333;
  font-size: 16px;
}

.amount-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.available-amount {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.available-amount .label {
  color: #666;
  font-size: 14px;
}

.available-amount .value {
  color: #67C23A;
  font-size: 18px;
  font-weight: 600;
  margin-left: 8px;
}

.amount-tips {
  margin-top: 8px;
}

.amount-tips p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.amount-tips .actual-amount {
  color: #409EFF;
  font-weight: 600;
}

.notice-section {
  margin-bottom: 30px;
}

.notice-section .el-alert__description p {
  margin: 4px 0;
  line-height: 1.5;
}

.form-actions {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.form-actions .el-button {
  margin-left: 10px;
}

@media (max-width: 768px) {
  .form-actions {
    text-align: center;
  }
  
  .form-actions .el-button {
    margin: 5px;
  }
}
</style>