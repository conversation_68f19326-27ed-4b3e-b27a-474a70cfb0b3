<template>
  <div class="page-title">
    <h2 class="title">{{ title }}</h2>
    <p v-if="description" class="description">{{ description }}</p>
  </div>
</template>

<script>
export default {
  name: 'PageTitle',
  props: {
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      default: ''
    }
  },
  watch: {
    title: {
      immediate: true,
      handler(newTitle) {
        // Update document title
        document.title = `${newTitle} - <PERSON>ay Merchant`
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-title {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;

  .title {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 500;
    color: #262626;
    line-height: 28px;
  }

  .description {
    margin: 0;
    color: #8c8c8c;
    font-size: 14px;
    line-height: 22px;
  }
}

@media (max-width: 768px) {
  .page-title {
    .title {
      font-size: 18px;
      line-height: 24px;
    }
    
    .description {
      font-size: 13px;
      line-height: 20px;
    }
  }
}
</style>