<template>
  <div class="kyc-status">
    <div class="page-header">
      <h2>KYC 审核状态</h2>
      <p>请完成身份认证以使用完整功能</p>
    </div>
    
    <el-card class="status-card">
      <div class="status-content">
        <div class="status-icon">
          <i :class="statusIcon" :style="{ color: statusColor }"></i>
        </div>
        <div class="status-info">
          <h3>{{ statusText }}</h3>
          <p class="status-desc">{{ statusDesc }}</p>
          <div v-if="merchantInfo.updated_at" class="status-time">
            最后更新：{{ formatTime(merchantInfo.updated_at) }}
          </div>
        </div>
      </div>
      
      <!-- 审核进度 -->
      <div class="progress-section">
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step title="提交申请" description="填写基本信息"></el-step>
          <el-step title="上传资料" description="上传身份证和营业执照"></el-step>
          <el-step title="审核中" description="等待平台审核"></el-step>
          <el-step title="审核完成" description="开始使用服务"></el-step>
        </el-steps>
      </div>
      
      <!-- 商户基本信息 -->
      <div class="merchant-info">
        <h4>商户信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="商户名称">{{ merchantInfo.mch_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="商户号">{{ merchantInfo.mch_no || '-' }}</el-descriptions-item>
          <el-descriptions-item label="联系人">{{ merchantInfo.contact_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ merchantInfo.contact_tel || '-' }}</el-descriptions-item>
          <el-descriptions-item label="商户类型">
            {{ merchantInfo.type === 1 ? '个人商户' : merchantInfo.type === 2 ? '企业商户' : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ formatTime(merchantInfo.created_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-section">
        <el-button
          v-if="merchantInfo.state === 0"
          type="primary"
          @click="goToUpload"
        >
          上传认证资料
        </el-button>
        <el-button
          v-if="merchantInfo.state === 3"
          type="primary"
          @click="resubmit"
        >
          重新提交
        </el-button>
        <el-button
          v-if="merchantInfo.state === 2"
          @click="refreshStatus"
          :loading="loading"
        >
          刷新状态
        </el-button>
      </div>
    </el-card>
    
    <!-- KYC文档上传对话框 -->
    <el-dialog
      title="上传认证资料"
      :visible.sync="uploadDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <kyc-upload
        ref="kycUpload"
        @success="handleUploadSuccess"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getMerchantInfo, getKycStatus } from '@/api/merchant'
import KycUpload from './components/KycUpload.vue'

export default {
  name: 'KycStatus',
  components: {
    KycUpload
  },
  data() {
    return {
      loading: false,
      merchantInfo: {},
      uploadDialogVisible: false
    }
  },
  computed: {
    // 根据 t_merchant.state 字段确定状态
    // 0: 待提交, 1: 审核中, 2: 审核通过, 3: 审核拒绝
    statusText() {
      const statusMap = {
        0: '待提交资料',
        1: '审核中',
        2: '审核通过',
        3: '审核拒绝'
      }
      return statusMap[this.merchantInfo.state] || '未知状态'
    },
    statusDesc() {
      const descMap = {
        0: '请上传身份证和营业执照完成认证',
        1: '您的资料正在审核中，请耐心等待',
        2: '恭喜！您的认证已通过，可以正常使用所有功能',
        3: '很抱歉，您的认证未通过，请重新提交资料'
      }
      return descMap[this.merchantInfo.state] || ''
    },
    statusIcon() {
      const iconMap = {
        0: 'el-icon-warning',
        1: 'el-icon-loading',
        2: 'el-icon-success',
        3: 'el-icon-error'
      }
      return iconMap[this.merchantInfo.state] || 'el-icon-question'
    },
    statusColor() {
      const colorMap = {
        0: '#E6A23C',
        1: '#409EFF',
        2: '#67C23A',
        3: '#F56C6C'
      }
      return colorMap[this.merchantInfo.state] || '#909399'
    },
    currentStep() {
      const stepMap = {
        0: 1, // 待提交资料
        1: 2, // 审核中
        2: 3, // 审核通过
        3: 1  // 审核拒绝，回到第一步
      }
      return stepMap[this.merchantInfo.state] || 0
    }
  },
  created() {
    this.fetchMerchantInfo()
  },
  methods: {
    async fetchMerchantInfo() {
      this.loading = true
      try {
        const response = await getMerchantInfo()
        if (response.code === 0) {
          this.merchantInfo = response.data
        } else {
          this.$message.error(response.msg || '获取商户信息失败')
        }
      } catch (error) {
        console.error('Fetch merchant info error:', error)
        this.$message.error('获取商户信息失败')
      } finally {
        this.loading = false
      }
    },
    async refreshStatus() {
      await this.fetchMerchantInfo()
      this.$message.success('状态已刷新')
    },
    goToUpload() {
      this.uploadDialogVisible = true
    },
    resubmit() {
      this.uploadDialogVisible = true
    },
    handleUploadSuccess() {
      this.uploadDialogVisible = false
      this.fetchMerchantInfo()
      this.$message.success('资料提交成功，请等待审核')
    },
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.kyc-status {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #333;
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  font-size: 14px;
}

.status-card {
  margin-bottom: 20px;
}

.status-content {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-icon {
  margin-right: 20px;
}

.status-icon i {
  font-size: 48px;
}

.status-info h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 20px;
}

.status-desc {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.status-time {
  color: #999;
  font-size: 12px;
}

.progress-section {
  margin-bottom: 30px;
  padding: 20px 0;
}

.merchant-info {
  margin-bottom: 30px;
}

.merchant-info h4 {
  margin-bottom: 16px;
  color: #333;
}

.action-section {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.action-section .el-button {
  margin: 0 10px;
}
</style>