<template>
  <el-dropdown @command="handleLanguageChange" class="language-switcher">
    <span class="el-dropdown-link">
      <i class="el-icon-s-grid"></i>
      {{ currentLanguageLabel }}
      <i class="el-icon-arrow-down el-icon--right"></i>
    </span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item command="zh">{{ $t('common.chinese') }}</el-dropdown-item>
      <el-dropdown-item command="en">{{ $t('common.english') }}</el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  name: 'LanguageSwitcher',
  computed: {
    currentLanguageLabel() {
      return this.$i18n.locale === 'zh' ? '中文' : 'English'
    }
  },
  methods: {
    handleLanguageChange(language) {
      if (language === this.$i18n.locale) {
        return
      }
      
      this.$i18n.locale = language
      localStorage.setItem('language', language)
      
      // 更新Element UI的语言
      this.$ELEMENT.locale = language === 'zh' 
        ? require('element-ui/lib/locale/lang/zh-CN').default
        : require('element-ui/lib/locale/lang/en').default
      
      this.$message.success(this.$t('common.success'))
      
      // 触发全局事件，通知其他组件语言已切换
      this.$root.$emit('language-changed', language)
    }
  }
}
</script>

<style scoped>
.language-switcher {
  cursor: pointer;
  color: #606266;
}

.language-switcher:hover {
  color: #409EFF;
}

.el-dropdown-link {
  display: flex;
  align-items: center;
  font-size: 14px;
}
</style>