import { login, logout, getUserInfo, refreshToken } from '@/api/auth'

const state = {
  token: localStorage.getItem('token') || '',
  refreshToken: localStorage.getItem('refreshToken') || '',
  userInfo: JSON.parse(localStorage.getItem('userInfo') || '{}'),
  permissions: [],
  tokenExpireTime: localStorage.getItem('tokenExpireTime') || 0
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
    localStorage.setItem('token', token)
  },
  SET_REFRESH_TOKEN(state, refreshToken) {
    state.refreshToken = refreshToken
    localStorage.setItem('refreshToken', refreshToken)
  },
  SET_TOKEN_EXPIRE_TIME(state, expireTime) {
    state.tokenExpireTime = expireTime
    localStorage.setItem('tokenExpireTime', expireTime)
  },
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
    localStorage.setItem('userInfo', JSON.stringify(userInfo))
  },
  SET_PERMISSIONS(state, permissions) {
    state.permissions = permissions
  },
  CLEAR_USER_DATA(state) {
    state.token = ''
    state.refreshToken = ''
    state.userInfo = {}
    state.permissions = []
    state.tokenExpireTime = 0
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('tokenExpireTime')
  }
}

const actions = {
  // 登录
  async login({ commit }, userInfo) {
    try {
      const response = await login(userInfo)
      if (response.code === 0) {
        const { token, refresh_token, expires_in } = response.data
        const expireTime = Date.now() + expires_in * 1000
        
        commit('SET_TOKEN', token)
        commit('SET_REFRESH_TOKEN', refresh_token)
        commit('SET_TOKEN_EXPIRE_TIME', expireTime)
        
        return response
      }
      return response
    } catch (error) {
      throw error
    }
  },

  // 获取用户信息
  async getUserInfo({ commit }) {
    try {
      const response = await getUserInfo()
      if (response.code === 0) {
        commit('SET_USER_INFO', response.data)
        // 如果返回权限信息，也设置权限
        if (response.data.permissions) {
          commit('SET_PERMISSIONS', response.data.permissions)
        }
      }
      return response
    } catch (error) {
      throw error
    }
  },

  // 刷新token
  async refreshToken({ commit, state }) {
    try {
      const response = await refreshToken()
      if (response.code === 0) {
        const { token, refresh_token, expires_in } = response.data
        const expireTime = Date.now() + expires_in * 1000
        
        commit('SET_TOKEN', token)
        commit('SET_REFRESH_TOKEN', refresh_token)
        commit('SET_TOKEN_EXPIRE_TIME', expireTime)
        
        return response
      }
      return response
    } catch (error) {
      // 刷新失败，清除用户数据
      commit('CLEAR_USER_DATA')
      throw error
    }
  },

  // 登出
  async logout({ commit }) {
    try {
      await logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      commit('CLEAR_USER_DATA')
    }
  },

  // 设置权限
  setPermissions({ commit }, permissions) {
    commit('SET_PERMISSIONS', permissions)
  },

  // 检查token是否即将过期
  checkTokenExpire({ dispatch, state }) {
    const now = Date.now()
    const expireTime = parseInt(state.tokenExpireTime)
    // 如果token在5分钟内过期，自动刷新
    if (expireTime - now < 5 * 60 * 1000 && state.refreshToken) {
      return dispatch('refreshToken')
    }
    return Promise.resolve()
  }
}

const getters = {
  token: state => state.token,
  isLoggedIn: state => !!state.token,
  userInfo: state => state.userInfo,
  permissions: state => state.permissions
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}