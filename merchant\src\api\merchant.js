import request from '@/utils/request'

// 商户注册
export function registerMerchant(data) {
  return request({
    url: '/api/merchant/register',
    method: 'post',
    data: {
      mch_name: data.mch_name,
      contact_name: data.contact_name,
      contact_tel: data.contact_tel,
      type: data.type || 1
    }
  })
}

// 获取商户信息
export function getMerchantInfo() {
  return request({
    url: '/api/merchant/info',
    method: 'get'
  })
}

// 更新商户信息
export function updateMerchantInfo(data) {
  return request({
    url: '/api/merchant/update',
    method: 'put',
    data: {
      mch_name: data.mch_name,
      contact_name: data.contact_name,
      contact_tel: data.contact_tel
    }
  })
}

// 获取KYC审核状态
export function getKycStatus() {
  return request({
    url: '/api/merchant/kyc/status',
    method: 'get'
  })
}

// 提交KYC审核材料
export function submitKycDocuments(data) {
  return request({
    url: '/api/merchant/kyc/submit',
    method: 'post',
    data: {
      id_card_front: data.id_card_front,
      id_card_back: data.id_card_back,
      business_license: data.business_license
    }
  })
}

// 上传文件
export function uploadFile(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/api/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}