import request from '@/utils/request'

// 获取支付通道列表
export function getChannelList(params) {
  return request({
    url: '/api/channel/list',
    method: 'get',
    params: {
      page: params.page || 1,
      size: params.size || 20,
      channel_name: params.channel_name,
      status: params.status,
      pay_method: params.pay_method
    }
  })
}

// 获取通道详情
export function getChannelDetail(channelId) {
  return request({
    url: `/api/channel/detail/${channelId}`,
    method: 'get'
  })
}

// 更新通道配置
export function updateChannelConfig(channelId, data) {
  return request({
    url: `/api/channel/config/${channelId}`,
    method: 'put',
    data: {
      config: data.config,
      rate: data.rate,
      min_amount: data.min_amount,
      max_amount: data.max_amount,
      status: data.status
    }
  })
}

// 测试通道连接
export function testChannelConnection(channelId) {
  return request({
    url: `/api/channel/test/${channelId}`,
    method: 'post'
  })
}

// 获取通道统计
export function getChannelStats(channelId, params) {
  return request({
    url: `/api/channel/stats/${channelId}`,
    method: 'get',
    params: {
      date_start: params.date_start,
      date_end: params.date_end
    }
  })
}

// 获取可用通道类型
export function getChannelTypes() {
  return request({
    url: '/api/channel/types',
    method: 'get'
  })
}

// 通道状态枚举
export const CHANNEL_STATUS = {
  0: { text: '禁用', type: 'danger' },
  1: { text: '启用', type: 'success' },
  2: { text: '维护中', type: 'warning' }
}

// 支付方式枚举
export const PAY_METHOD = {
  1: { text: '外卡支付', icon: 'el-icon-bank-card' },
  2: { text: 'USDT支付', icon: 'el-icon-coin' },
  3: { text: '本地支付', icon: 'el-icon-wallet' }
}

// 获取通道状态选项
export function getChannelStatusOptions() {
  return Object.keys(CHANNEL_STATUS).map(key => ({
    value: parseInt(key),
    label: CHANNEL_STATUS[key].text
  }))
}

// 获取支付方式选项
export function getPayMethodOptions() {
  return Object.keys(PAY_METHOD).map(key => ({
    value: parseInt(key),
    label: PAY_METHOD[key].text
  }))
}