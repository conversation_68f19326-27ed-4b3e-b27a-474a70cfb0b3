import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import user from './modules/user'
import merchant from './modules/merchant'
import permission from './modules/permission'

Vue.use(Vuex)

const getters = {
  sidebar: state => state.app.sidebar,
  device: state => state.app.device,
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  roles: state => state.user.roles
}

export default new Vuex.Store({
  modules: {
    app,
    user,
    merchant,
    permission
  },
  getters,
  strict: process.env.NODE_ENV !== 'production'
})