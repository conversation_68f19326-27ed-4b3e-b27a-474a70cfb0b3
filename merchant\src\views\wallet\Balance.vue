<template>
  <div class="wallet-balance">
    <div class="page-header">
      <h2>钱包管理</h2>
      <p>查看账户余额和资金流水</p>
    </div>
    
    <!-- 余额卡片 -->
    <div class="balance-cards">
      <el-card class="balance-card main-balance">
        <div class="balance-content">
          <div class="balance-info">
            <div class="balance-label">账户余额</div>
            <div class="balance-value">¥{{ formatMoney(walletInfo.balance) }}</div>
            <div class="balance-desc">可用余额</div>
          </div>
          <div class="balance-icon">
            <i class="el-icon-wallet"></i>
          </div>
        </div>
        <div class="balance-actions">
          <el-button type="primary" @click="goToRecharge">USDT充值</el-button>
          <el-button @click="goToWithdraw" disabled>提现</el-button>
        </div>
      </el-card>
      
      <el-card class="balance-card">
        <div class="balance-content">
          <div class="balance-info">
            <div class="balance-label">冻结金额</div>
            <div class="balance-value frozen">¥{{ formatMoney(walletInfo.frozen_amount) }}</div>
            <div class="balance-desc">结算中金额</div>
          </div>
          <div class="balance-icon">
            <i class="el-icon-lock"></i>
          </div>
        </div>
      </el-card>
      
      <el-card class="balance-card">
        <div class="balance-content">
          <div class="balance-info">
            <div class="balance-label">今日收入</div>
            <div class="balance-value income">¥{{ formatMoney(walletInfo.today_income) }}</div>
            <div class="balance-desc">今日交易收入</div>
          </div>
          <div class="balance-icon">
            <i class="el-icon-arrow-down"></i>
          </div>
        </div>
      </el-card>
      
      <el-card class="balance-card">
        <div class="balance-content">
          <div class="balance-info">
            <div class="balance-label">累计收入</div>
            <div class="balance-value total">¥{{ formatMoney(walletInfo.total_income) }}</div>
            <div class="balance-desc">历史总收入</div>
          </div>
          <div class="balance-icon">
            <i class="el-icon-data-line"></i>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form
        ref="searchForm"
        :model="searchForm"
        :inline="true"
        label-width="80px"
        class="search-form"
      >
        <el-form-item label="交易类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in typeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="交易时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 350px"
          ></el-date-picker>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 资金流水 -->
    <el-card class="table-card">
      <div slot="header">
        <span>资金流水</span>
      </div>
      <el-table
        v-loading="loading"
        :data="transactionList"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="transaction_id" label="流水号" width="180">
          <template slot-scope="scope">
            <span class="transaction-id">{{ scope.row.transaction_id }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="交易类型" width="100">
          <template slot-scope="scope">
            <div class="type-cell">
              <i :class="getTypeIcon(scope.row.type)" :style="{ color: getTypeColor(scope.row.type) }"></i>
              <span>{{ getTypeText(scope.row.type) }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="amount" label="交易金额" width="120">
          <template slot-scope="scope">
            <span :class="getAmountClass(scope.row.type)">
              {{ getAmountPrefix(scope.row.type) }}¥{{ formatMoney(scope.row.amount) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="balance_after" label="余额" width="120">
          <template slot-scope="scope">
            <span class="balance-after">¥{{ formatMoney(scope.row.balance_after) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="交易描述" min-width="200">
          <template slot-scope="scope">
            {{ scope.row.description || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="交易时间" width="160">
          <template slot-scope="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        ></el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import { 
  getWalletBalance, 
  getTransactionList, 
  TRANSACTION_TYPE, 
  getTransactionTypeOptions 
} from '@/api/wallet'

export default {
  name: 'WalletBalance',
  data() {
    return {
      loading: false,
      walletInfo: {
        balance: 0,
        frozen_amount: 0,
        today_income: 0,
        total_income: 0
      },
      transactionList: [],
      searchForm: {
        type: ''
      },
      dateRange: [],
      pagination: {
        page: 1,
        size: 20,
        total: 0
      }
    }
  },
  computed: {
    typeOptions() {
      return getTransactionTypeOptions()
    },
    searchParams() {
      const params = {
        page: this.pagination.page,
        size: this.pagination.size,
        ...this.searchForm
      }
      
      if (this.dateRange && this.dateRange.length === 2) {
        params.created_at_start = this.dateRange[0]
        params.created_at_end = this.dateRange[1]
      }
      
      return params
    }
  },
  created() {
    this.fetchWalletBalance()
    this.fetchTransactionList()
  },
  methods: {
    async fetchWalletBalance() {
      try {
        const response = await getWalletBalance()
        if (response.code === 0) {
          this.walletInfo = response.data
        } else {
          this.$message.error(response.msg || '获取钱包信息失败')
        }
      } catch (error) {
        console.error('Fetch wallet balance error:', error)
        this.$message.error('获取钱包信息失败')
      }
    },
    async fetchTransactionList() {
      this.loading = true
      try {
        const response = await getTransactionList(this.searchParams)
        if (response.code === 0) {
          this.transactionList = response.data.list || []
          this.pagination.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || '获取交易记录失败')
        }
      } catch (error) {
        console.error('Fetch transaction list error:', error)
        this.$message.error('获取交易记录失败')
      } finally {
        this.loading = false
      }
    },
    handleSearch() {
      this.pagination.page = 1
      this.fetchTransactionList()
    },
    handleReset() {
      this.searchForm = {
        type: ''
      }
      this.dateRange = []
      this.pagination.page = 1
      this.fetchTransactionList()
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.fetchTransactionList()
    },
    handleCurrentChange(page) {
      this.pagination.page = page
      this.fetchTransactionList()
    },
    goToRecharge() {
      this.$router.push('/wallet/recharge')
    },
    goToWithdraw() {
      this.$message.info('提现功能暂未开放')
    },
    getTypeText(type) {
      return TRANSACTION_TYPE[type]?.text || '未知'
    },
    getTypeIcon(type) {
      return TRANSACTION_TYPE[type]?.icon || 'el-icon-question'
    },
    getTypeColor(type) {
      const colorMap = {
        success: '#67C23A',
        warning: '#E6A23C',
        danger: '#F56C6C',
        info: '#409EFF'
      }
      return colorMap[TRANSACTION_TYPE[type]?.type] || '#909399'
    },
    getAmountClass(type) {
      const typeInfo = TRANSACTION_TYPE[type]
      if (!typeInfo) return ''
      
      const classMap = {
        success: 'amount-positive',
        warning: 'amount-warning',
        danger: 'amount-negative',
        info: 'amount-info'
      }
      return classMap[typeInfo.type] || ''
    },
    getAmountPrefix(type) {
      // 收入类型显示+，支出类型显示-
      return [1, 3, 6].includes(type) ? '+' : '-'
    },
    formatMoney(amount) {
      return Number(amount || 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.wallet-balance {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #333;
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  font-size: 14px;
}

.balance-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.balance-card {
  transition: transform 0.2s;
}

.balance-card:hover {
  transform: translateY(-2px);
}

.main-balance {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.main-balance :deep(.el-card__body) {
  padding: 30px;
}

.balance-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.balance-info {
  flex: 1;
}

.balance-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.balance-value {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 4px;
}

.balance-value.frozen {
  color: #E6A23C;
}

.balance-value.income {
  color: #67C23A;
}

.balance-value.total {
  color: #409EFF;
}

.balance-desc {
  font-size: 12px;
  opacity: 0.8;
}

.balance-icon {
  font-size: 48px;
  opacity: 0.3;
}

.balance-actions {
  text-align: center;
}

.balance-actions .el-button {
  margin: 0 5px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-card {
  margin-bottom: 20px;
}

.transaction-id {
  font-family: monospace;
  font-size: 12px;
  color: #666;
}

.type-cell {
  display: flex;
  align-items: center;
}

.type-cell i {
  margin-right: 6px;
}

.amount-positive {
  color: #67C23A;
  font-weight: 600;
}

.amount-negative {
  color: #F56C6C;
  font-weight: 600;
}

.amount-warning {
  color: #E6A23C;
  font-weight: 600;
}

.amount-info {
  color: #409EFF;
  font-weight: 600;
}

.balance-after {
  font-weight: 600;
  color: #333;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

@media (max-width: 768px) {
  .balance-cards {
    grid-template-columns: 1fr;
  }
  
  .balance-content {
    flex-direction: column;
    text-align: center;
  }
  
  .balance-icon {
    margin-top: 16px;
  }
  
  .search-form .el-form-item {
    margin-bottom: 10px;
  }
  
  .pagination-wrapper {
    text-align: center;
  }
}
</style>