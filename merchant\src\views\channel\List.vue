<template>
  <div class="channel-list">
    <div class="page-header">
      <h2>通道管理</h2>
      <p>管理和配置支付通道</p>
    </div>
    
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form
        ref="searchForm"
        :model="searchForm"
        :inline="true"
        label-width="80px"
        class="search-form"
      >
        <el-form-item label="通道名称">
          <el-input
            v-model="searchForm.channel_name"
            placeholder="请输入通道名称"
            clearable
            style="width: 200px"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="支付方式">
          <el-select
            v-model="searchForm.pay_method"
            placeholder="请选择支付方式"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in payMethodOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="通道状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 通道列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="channelList"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="channel_name" label="通道名称" width="150" fixed="left">
          <template slot-scope="scope">
            <div class="channel-name">
              <i :class="getPayMethodIcon(scope.row.pay_method)"></i>
              <span>{{ scope.row.channel_name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="pay_method" label="支付方式" width="120">
          <template slot-scope="scope">
            {{ getPayMethodText(scope.row.pay_method) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="rate" label="费率" width="100">
          <template slot-scope="scope">
            {{ (scope.row.rate * 100).toFixed(2) }}%
          </template>
        </el-table-column>
        
        <el-table-column prop="min_amount" label="最小金额" width="120">
          <template slot-scope="scope">
            ¥{{ formatMoney(scope.row.min_amount) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="max_amount" label="最大金额" width="120">
          <template slot-scope="scope">
            ¥{{ formatMoney(scope.row.max_amount) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="success_rate" label="成功率" width="100">
          <template slot-scope="scope">
            <span :class="getSuccessRateClass(scope.row.success_rate)">
              {{ (scope.row.success_rate * 100).toFixed(1) }}%
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="updated_at" label="更新时间" width="160">
          <template slot-scope="scope">
            {{ formatTime(scope.row.updated_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="configChannel(scope.row)">
              配置
            </el-button>
            <el-button size="mini" type="success" @click="testChannel(scope.row)" :loading="scope.row.testing">
              测试
            </el-button>
            <el-button size="mini" @click="viewStats(scope.row)">
              统计
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        ></el-pagination>
      </div>
    </el-card>
    
    <!-- 通道配置对话框 -->
    <el-dialog
      title="通道配置"
      :visible.sync="configDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <channel-config
        ref="channelConfig"
        :channel-info="currentChannel"
        @success="handleConfigSuccess"
        @cancel="configDialogVisible = false"
      />
    </el-dialog>
    
    <!-- 统计对话框 -->
    <el-dialog
      title="通道统计"
      :visible.sync="statsDialogVisible"
      width="900px"
    >
      <channel-stats
        ref="channelStats"
        :channel-info="currentChannel"
      />
    </el-dialog>
  </div>
</template>

<script>
import { 
  getChannelList, 
  testChannelConnection,
  CHANNEL_STATUS, 
  PAY_METHOD,
  getChannelStatusOptions,
  getPayMethodOptions
} from '@/api/channel'
import ChannelConfig from './components/ChannelConfig.vue'
import ChannelStats from './components/ChannelStats.vue'

export default {
  name: 'ChannelList',
  components: {
    ChannelConfig,
    ChannelStats
  },
  data() {
    return {
      loading: false,
      channelList: [],
      currentChannel: {},
      configDialogVisible: false,
      statsDialogVisible: false,
      searchForm: {
        channel_name: '',
        pay_method: '',
        status: ''
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      }
    }
  },
  computed: {
    statusOptions() {
      return getChannelStatusOptions()
    },
    payMethodOptions() {
      return getPayMethodOptions()
    },
    searchParams() {
      return {
        page: this.pagination.page,
        size: this.pagination.size,
        ...this.searchForm
      }
    }
  },
  created() {
    this.fetchChannelList()
  },
  methods: {
    async fetchChannelList() {
      this.loading = true
      try {
        const response = await getChannelList(this.searchParams)
        if (response.code === 0) {
          this.channelList = response.data.list || []
          this.pagination.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || '获取通道列表失败')
        }
      } catch (error) {
        console.error('Fetch channel list error:', error)
        this.$message.error('获取通道列表失败')
      } finally {
        this.loading = false
      }
    },
    handleSearch() {
      this.pagination.page = 1
      this.fetchChannelList()
    },
    handleReset() {
      this.searchForm = {
        channel_name: '',
        pay_method: '',
        status: ''
      }
      this.pagination.page = 1
      this.fetchChannelList()
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.fetchChannelList()
    },
    handleCurrentChange(page) {
      this.pagination.page = page
      this.fetchChannelList()
    },
    configChannel(row) {
      this.currentChannel = { ...row }
      this.configDialogVisible = true
    },
    async testChannel(row) {
      this.$set(row, 'testing', true)
      try {
        const response = await testChannelConnection(row.id)
        if (response.code === 0) {
          this.$message.success('通道连接测试成功！')
        } else {
          this.$message.error(response.msg || '通道连接测试失败')
        }
      } catch (error) {
        console.error('Test channel error:', error)
        this.$message.error('通道连接测试失败')
      } finally {
        this.$set(row, 'testing', false)
      }
    },
    viewStats(row) {
      this.currentChannel = { ...row }
      this.statsDialogVisible = true
    },
    handleConfigSuccess() {
      this.configDialogVisible = false
      this.fetchChannelList()
      this.$message.success('通道配置更新成功！')
    },
    getStatusText(status) {
      return CHANNEL_STATUS[status]?.text || '未知'
    },
    getStatusType(status) {
      return CHANNEL_STATUS[status]?.type || 'info'
    },
    getPayMethodText(method) {
      return PAY_METHOD[method]?.text || '未知'
    },
    getPayMethodIcon(method) {
      return PAY_METHOD[method]?.icon || 'el-icon-question'
    },
    getSuccessRateClass(rate) {
      if (rate >= 0.95) return 'success-rate-high'
      if (rate >= 0.8) return 'success-rate-medium'
      return 'success-rate-low'
    },
    formatMoney(amount) {
      return Number(amount || 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>
<style sc
oped>
.channel-list {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #333;
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.table-card {
  margin-bottom: 20px;
}

.channel-name {
  display: flex;
  align-items: center;
}

.channel-name i {
  margin-right: 8px;
  font-size: 16px;
  color: #409EFF;
}

.success-rate-high {
  color: #67C23A;
  font-weight: 600;
}

.success-rate-medium {
  color: #E6A23C;
  font-weight: 600;
}

.success-rate-low {
  color: #F56C6C;
  font-weight: 600;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

@media (max-width: 768px) {
  .search-form .el-form-item {
    margin-bottom: 10px;
  }
  
  .pagination-wrapper {
    text-align: center;
  }
}
</style>