<template>
  <el-form
    ref="form"
    :model="formData"
    :rules="formRules"
    :label-width="labelWidth"
    :label-position="labelPosition"
    :size="size"
    :disabled="loading"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <slot />
    
    <!-- 表单操作按钮 -->
    <el-form-item v-if="showActions" :class="actionClass">
      <el-button
        v-if="showCancel"
        :size="size"
        @click="handleCancel"
      >
        {{ cancelText }}
      </el-button>
      
      <el-button
        v-if="showReset"
        :size="size"
        @click="handleReset"
      >
        {{ resetText }}
      </el-button>
      
      <el-button
        type="primary"
        :size="size"
        :loading="loading"
        @click="handleSubmit"
      >
        {{ loading ? loadingText : submitText }}
      </el-button>
    </el-form-item>
    
    <!-- 错误提示 -->
    <div v-if="errorMessage" class="form-error">
      <i class="el-icon-warning"></i>
      {{ errorMessage }}
    </div>
  </el-form>
</template>

<script>
import { formValidationMixin } from '@/utils/validate-rules'

export default {
  name: 'BaseForm',
  mixins: [formValidationMixin],
  props: {
    // 表单数据
    value: {
      type: Object,
      default: () => ({})
    },
    
    // 验证规则
    rules: {
      type: Object,
      default: () => ({})
    },
    
    // 标签宽度
    labelWidth: {
      type: String,
      default: '100px'
    },
    
    // 标签位置
    labelPosition: {
      type: String,
      default: 'right'
    },
    
    // 表单尺寸
    size: {
      type: String,
      default: 'medium'
    },
    
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      default: true
    },
    
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      default: true
    },
    
    // 是否显示重置按钮
    showReset: {
      type: Boolean,
      default: false
    },
    
    // 按钮文本
    submitText: {
      type: String,
      default: '提交'
    },
    
    cancelText: {
      type: String,
      default: '取消'
    },
    
    resetText: {
      type: String,
      default: '重置'
    },
    
    loadingText: {
      type: String,
      default: '提交中...'
    },
    
    // 操作按钮样式类
    actionClass: {
      type: String,
      default: 'form-actions'
    },
    
    // 自动验证
    autoValidate: {
      type: Boolean,
      default: true
    },
    
    // 提交前验证
    validateBeforeSubmit: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      formData: {},
      errorMessage: ''
    }
  },
  
  computed: {
    formRules() {
      return this.rules
    }
  },
  
  watch: {
    value: {
      handler(newVal) {
        this.formData = { ...newVal }
      },
      immediate: true,
      deep: true
    },
    
    formData: {
      handler(newVal) {
        this.$emit('input', newVal)
        this.$emit('change', newVal)
      },
      deep: true
    }
  },
  
  methods: {
    // 提交表单
    async handleSubmit() {
      this.errorMessage = ''
      
      try {
        // 验证表单
        if (this.validateBeforeSubmit) {
          const isValid = await this.validateForm()
          if (!isValid) {
            return
          }
        }
        
        // 触发提交事件
        this.$emit('submit', this.formData)
        
      } catch (error) {
        this.errorMessage = error.message || '表单验证失败'
        this.$emit('error', error)
      }
    },
    
    // 取消操作
    handleCancel() {
      this.$emit('cancel')
    },
    
    // 重置表单
    handleReset() {
      this.resetForm()
      this.errorMessage = ''
      this.$emit('reset')
    },
    
    // 验证表单
    async validate() {
      return await this.validateForm()
    },
    
    // 验证字段
    async validateField(field) {
      return await this.validateField(field)
    },
    
    // 清除验证
    clearValidate() {
      this.clearValidation()
      this.errorMessage = ''
    },
    
    // 重置表单
    reset() {
      this.handleReset()
    },
    
    // 设置字段值
    setFieldValue(field, value) {
      this.$set(this.formData, field, value)
    },
    
    // 获取字段值
    getFieldValue(field) {
      return this.formData[field]
    },
    
    // 设置错误消息
    setError(message) {
      this.errorMessage = message
    },
    
    // 清除错误消息
    clearError() {
      this.errorMessage = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.form-actions {
  text-align: center;
  margin-top: 20px;
  
  .el-button {
    margin: 0 8px;
  }
}

.form-error {
  color: #f56c6c;
  font-size: 14px;
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  display: flex;
  align-items: center;
  
  i {
    margin-right: 8px;
  }
}

// 响应式样式
@media (max-width: 768px) {
  .form-actions {
    .el-button {
      width: 100%;
      margin: 4px 0;
    }
  }
}
</style>