import { hasPermission, hasRole } from '@/utils/permission'

export default {
  methods: {
    /**
     * 检查是否有权限
     * @param {Array|String} permission 权限码
     * @returns {Boolean}
     */
    $hasPermission(permission) {
      return hasPermission(permission)
    },

    /**
     * 检查是否有角色
     * @param {Array|String} role 角色
     * @returns {Boolean}
     */
    $hasRole(role) {
      return hasRole(role)
    }
  }
}