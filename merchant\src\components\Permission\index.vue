<template>
  <div v-if="hasAccess">
    <slot />
  </div>
  <div v-else-if="$slots.fallback">
    <slot name="fallback" />
  </div>
</template>

<script>
import { hasPermission, hasRole } from '@/utils/permission'
import AuthManager from '@/utils/auth'

export default {
  name: 'Permission',
  props: {
    // 权限码
    permission: {
      type: [String, Array],
      default: null
    },
    // 角色
    role: {
      type: [String, Array],
      default: null
    },
    // 复合权限条件
    condition: {
      type: Object,
      default: null
    },
    // 逻辑关系：AND 或 OR
    logic: {
      type: String,
      default: 'OR',
      validator: value => ['AND', 'OR'].includes(value)
    }
  },
  computed: {
    hasAccess() {
      // 如果设置了复合权限条件
      if (this.condition) {
        return AuthManager.checkComplexPermission(this.condition)
      }
      
      let permissionResult = true
      let roleResult = true
      
      // 检查权限
      if (this.permission !== null) {
        if (Array.isArray(this.permission)) {
          permissionResult = this.logic === 'AND'
            ? AuthManager.hasAllPermissions(this.permission)
            : AuthManager.hasAnyPermission(this.permission)
        } else {
          permissionResult = hasPermission(this.permission)
        }
      }
      
      // 检查角色
      if (this.role !== null) {
        if (Array.isArray(this.role)) {
          roleResult = this.logic === 'AND'
            ? AuthManager.hasAllRoles(this.role)
            : AuthManager.hasAnyRole(this.role)
        } else {
          roleResult = hasRole(this.role)
        }
      }
      
      // 如果只设置了权限或只设置了角色，直接返回对应结果
      if (this.permission !== null && this.role === null) {
        return permissionResult
      }
      if (this.role !== null && this.permission === null) {
        return roleResult
      }
      
      // 如果同时设置了权限和角色
      return this.logic === 'AND' 
        ? permissionResult && roleResult
        : permissionResult || roleResult
    }
  }
}
</script>