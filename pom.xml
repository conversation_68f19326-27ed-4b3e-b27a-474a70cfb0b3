<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion> <!-- POM模型版本 -->

    <groupId>com.king</groupId> <!-- 组织名, 类似于包名 -->
    <artifactId>cloudpay</artifactId>  <!-- 项目名称  -->
    <packaging>pom</packaging> <!-- 项目的最终打包类型/发布形式, 可选[jar, war, pom, maven-plugin]等 -->

    <name>cloudpay</name>
    <version>Final</version> <!-- pom版本号/项目总版本号， 每个子项目引入的版本号必须一致。  最外层的pom.xml版本号保持不变，始终为Final版本。 更新版本请更改isys.version属性  -->
    <description>cloudpay支付系统</description> <!-- 项目描述 -->

    <!-- 继承：Spring Boot Parent -->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.4.8</version>
    </parent>

    <!-- 声明子项目 -->
    <modules>
        <module>cloudpay-z-codegen</module>    <!-- 代码生成器 -->

        <module>cloudpay-core</module>  <!-- 基础函数, 包含工具类等 -->
        <module>cloudpay-service</module>  <!-- db service等 -->

        <module>cloudpay-manager</module>    <!-- 运营平台管理端 -->
        <module>cloudpay-merchant</module>    <!-- 商户平台管理端 -->
        <module>cloudpay-carddealer</module>    <!-- 卡商平台管理端 -->
        <module>cloudpay-payment</module>    <!-- 支付统一网关 -->

        <module>cloudpay-components</module>
    </modules>

    <!-- 配置属性声明, 支持自定义参数 -->
    <properties>

        <isys.version>1.13.0</isys.version> <!-- 指定当前[项目]版本号 -->

        <java.version>1.8</java.version> <!-- 指定java版本号 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding> <!-- 项目构建输出编码 -->
        <druid.version>1.2.4</druid.version>
        <!-- 其他工具包 -->
        <cloudpay.sdk.java.version>1.3.0</cloudpay.sdk.java.version>
        <fastjson.version>1.2.76</fastjson.version> <!-- fastjson -->
        <mybatis.plus.starter.version>3.4.2</mybatis.plus.starter.version>  <!-- mybatis plus -->
        <hutool.util.version>5.7.16</hutool.util.version>  <!-- hutool -->
        <spring.security.version>5.4.7</spring.security.version> <!-- 用于core的scope依赖 -->
        <jjwt.version>0.9.1</jjwt.version>
        <binarywang.weixin.java.version>4.1.0</binarywang.weixin.java.version>
        <rocketmq.spring.boot.starter.version>2.2.0</rocketmq.spring.boot.starter.version>
        <aliyun-openservices-ons-client.version>1.8.8.1.Final</aliyun-openservices-ons-client.version>

        <mysql.version>8.0.28</mysql.version> <!-- 覆写 spring-boot-dependencies 的依赖版本号 -->

    </properties>


    <!-- 依赖包管理， 按需添加 -->
    <dependencyManagement>
        <dependencies>

            <!-- 依赖 [ core ]包。  -->
            <dependency>
                <groupId>com.king</groupId>
                <artifactId>cloudpay-core</artifactId>
                <version>${isys.version}</version>
            </dependency>

            <!-- [ service ]包, 会自动传递依赖[ core ]包。  -->
            <dependency>
                <groupId>com.king</groupId>
                <artifactId>cloudpay-service</artifactId>
                <version>${isys.version}</version>
            </dependency>

            <!-- [ oss ]包  -->
            <dependency>
                <groupId>com.king</groupId>
                <artifactId>cloudpay-components-oss</artifactId>
                <version>${isys.version}</version>
            </dependency>

            <!-- [ mq ]包  -->
            <dependency>
                <groupId>com.king</groupId>
                <artifactId>cloudpay-components-mq</artifactId>
                <version>${isys.version}</version>
            </dependency>

            <!-- alibaba FastJSON -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- Spring Security -->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring.security.version}</version>
            </dependency>

            <!-- JWT  -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!--wx_pay  https://github.com/wechat-group/WxJava  -->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${binarywang.weixin.java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${binarywang.weixin.java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.starter.version}</version>
            </dependency>

            <!-- 生成二维码依赖 -->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.1.0</version>
            </dependency>

            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.1.0</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.1.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.alipay.sdk/alipay-sdk-java -->
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>4.22.22.ALL</version>
            </dependency>

            <!-- 阿里云oss组件  -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.13.0</version>
            </dependency>

            <!-- 添加对rocketMQ的支持  -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.spring.boot.starter.version}</version>
            </dependency>

            <!-- 添加对AliyunRocketMQ的支持 -->
            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>ons-client</artifactId>
                <version>${aliyun-openservices-ons-client.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 所有项目 项目依赖声明 -->
    <dependencies>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <scope>provided</scope> <!-- 编译阶段生效，不需要打入包中 -->
        </dependency>

        <!-- https://mvnrepository.com/artifact/cn.hutool/hutool-all -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.util.version}</version>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <!--引入插件-->
            </plugins>
        </pluginManagement>
    </build>


</project>
