<template>
  <div class="excel-export">
    <el-dropdown @command="handleExport" :disabled="loading">
      <el-button type="primary" :loading="loading">
        <i class="el-icon-download"></i>
        {{ $t('common.export') }}
        <i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="current">
          {{ $t('export.currentPage') || '导出当前页' }}
        </el-dropdown-item>
        <el-dropdown-item command="all">
          {{ $t('export.allData') || '导出全部数据' }}
        </el-dropdown-item>
        <el-dropdown-item command="selected" :disabled="!hasSelection">
          {{ $t('export.selectedData') || '导出选中数据' }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    
    <!-- 自定义导出对话框 -->
    <el-dialog
      :title="$t('export.customExport') || '自定义导出'"
      :visible.sync="customDialogVisible"
      width="600px"
    >
      <el-form :model="customForm" label-width="120px">
        <el-form-item :label="$t('export.filename') || '文件名'">
          <el-input v-model="customForm.filename" :placeholder="$t('export.filenamePlaceholder') || '请输入文件名'"></el-input>
        </el-form-item>
        
        <el-form-item :label="$t('export.columns') || '导出列'">
          <el-checkbox-group v-model="customForm.selectedColumns">
            <el-checkbox
              v-for="column in availableColumns"
              :key="column.key"
              :label="column.key"
            >
              {{ column.title || column.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item :label="$t('export.dateRange') || '日期范围'" v-if="showDateRange">
          <el-date-picker
            v-model="customForm.dateRange"
            type="daterange"
            :range-separator="$t('common.to') || '至'"
            :start-placeholder="$t('common.startDate') || '开始日期'"
            :end-placeholder="$t('common.endDate') || '结束日期'"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="customDialogVisible = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleCustomExport" :loading="loading">
          {{ $t('common.confirm') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ExcelExporter from '@/utils/excel'

export default {
  name: 'ExcelExport',
  props: {
    // 当前页数据
    data: {
      type: Array,
      default: () => []
    },
    // 选中的数据
    selectedData: {
      type: Array,
      default: () => []
    },
    // 列配置
    columns: {
      type: Array,
      required: true
    },
    // 文件名前缀
    filename: {
      type: String,
      default: 'export'
    },
    // 是否显示日期范围选择
    showDateRange: {
      type: Boolean,
      default: false
    },
    // 获取全部数据的方法
    fetchAllData: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      customDialogVisible: false,
      customForm: {
        filename: '',
        selectedColumns: [],
        dateRange: []
      }
    }
  },
  computed: {
    hasSelection() {
      return this.selectedData && this.selectedData.length > 0
    },
    availableColumns() {
      return this.columns.filter(col => col.exportable !== false)
    }
  },
  watch: {
    filename: {
      immediate: true,
      handler(val) {
        this.customForm.filename = val
      }
    },
    availableColumns: {
      immediate: true,
      handler(columns) {
        this.customForm.selectedColumns = columns.map(col => col.key)
      }
    }
  },
  methods: {
    async handleExport(command) {
      this.loading = true
      
      try {
        let exportData = []
        let filename = this.filename
        
        switch (command) {
          case 'current':
            exportData = this.data
            filename += '_current_page'
            break
          case 'all':
            if (this.fetchAllData) {
              exportData = await this.fetchAllData()
            } else {
              exportData = this.data
            }
            filename += '_all_data'
            break
          case 'selected':
            exportData = this.selectedData
            filename += '_selected'
            break
          case 'custom':
            this.showCustomDialog()
            return
        }
        
        await this.exportData(exportData, this.availableColumns, filename)
        this.$message.success(this.$t('export.exportSuccess') || '导出成功')
      } catch (error) {
        console.error('Export error:', error)
        this.$message.error(error.message || this.$t('export.exportFailed') || '导出失败')
      } finally {
        this.loading = false
      }
    },
    
    showCustomDialog() {
      this.customDialogVisible = true
      this.loading = false
    },
    
    async handleCustomExport() {
      if (!this.customForm.selectedColumns.length) {
        this.$message.warning(this.$t('export.selectColumns') || '请选择要导出的列')
        return
      }
      
      this.loading = true
      
      try {
        let exportData = this.data
        
        // 如果设置了日期范围，需要过滤数据
        if (this.customForm.dateRange && this.customForm.dateRange.length === 2) {
          const [startDate, endDate] = this.customForm.dateRange
          exportData = exportData.filter(item => {
            const itemDate = new Date(item.created_at || item.create_time)
            return itemDate >= new Date(startDate) && itemDate <= new Date(endDate)
          })
        }
        
        // 过滤选中的列
        const selectedColumns = this.availableColumns.filter(col => 
          this.customForm.selectedColumns.includes(col.key)
        )
        
        await this.exportData(exportData, selectedColumns, this.customForm.filename)
        
        this.$message.success(this.$t('export.exportSuccess') || '导出成功')
        this.customDialogVisible = false
      } catch (error) {
        console.error('Custom export error:', error)
        this.$message.error(error.message || this.$t('export.exportFailed') || '导出失败')
      } finally {
        this.loading = false
      }
    },
    
    async exportData(data, columns, filename) {
      if (!data || data.length === 0) {
        throw new Error(this.$t('export.noData') || '没有可导出的数据')
      }
      
      return ExcelExporter.exportToExcel(data, columns, filename)
    }
  }
}
</script>

<style scoped>
.excel-export {
  display: inline-block;
}

.dialog-footer {
  text-align: right;
}

.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
}

.el-checkbox-group .el-checkbox {
  margin-right: 20px;
  margin-bottom: 10px;
  width: calc(50% - 10px);
}
</style>