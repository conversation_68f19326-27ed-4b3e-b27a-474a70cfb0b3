<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="demo-form-inline">
      <el-form-item label="商户号">
        <el-input v-model="searchForm.mchNo" placeholder="请输入商户号" clearable />
      </el-form-item>
      <el-form-item label="商户名称">
        <el-input v-model="searchForm.mchName" placeholder="请输入商户名称" clearable />
      </el-form-item>
      <el-form-item label="服务商号">
        <el-input v-model="searchForm.isvNo" placeholder="请输入服务商号" clearable />
      </el-form-item>
      <el-form-item label="商户类型">
        <el-select v-model="searchForm.type" placeholder="请选择商户类型" clearable>
          <el-option label="普通商户" :value="1" />
          <el-option label="特约商户" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="searchForm.state" placeholder="请选择状态" clearable>
          <el-option label="停用" :value="0" />
          <el-option label="正常" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="success" @click="handleAdd" v-if="$hasPermission('ENT_MCH_INFO_ADD')">新增商户</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="mchNo" label="商户号" width="150" />
      <el-table-column prop="mchName" label="商户名称" width="200" />
      <el-table-column prop="mchShortName" label="商户简称" width="150" />
      <el-table-column prop="type" label="商户类型" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.type === 1 ? 'primary' : 'success'">
            {{ scope.row.type === 1 ? '普通商户' : '特约商户' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="state" label="状态" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.state === 1 ? 'success' : 'danger'">
            {{ scope.row.state === 1 ? '正常' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="contactName" label="联系人" width="120" />
      <el-table-column prop="contactTel" label="联系电话" width="130" />
      <el-table-column prop="raeTypeExchange" label="支付费率" width="100">
        <template slot-scope="scope">
          {{ (scope.row.raeTypeExchange * 100).toFixed(2) }}%
        </template>
      </el-table-column>
      <el-table-column prop="tranAccountsExchange" label="转账费率" width="100">
        <template slot-scope="scope">
          {{ (scope.row.tranAccountsExchange * 100).toFixed(2) }}%
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" width="160">
        <template slot-scope="scope">
          {{ $formatDate(scope.row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            @click="handleView(scope.row)"
            v-if="$hasPermission('ENT_MCH_INFO_VIEW')"
          >查看</el-button>
          <el-button
            size="mini"
            type="primary"
            @click="handleEdit(scope.row)"
            v-if="$hasPermission('ENT_MCH_INFO_EDIT')"
          >编辑</el-button>
          <el-button
            size="mini"
            type="warning"
            @click="handleWhiteIp(scope.row)"
            v-if="$hasPermission('ENT_MCH_INFO_EDIT')"
          >白名单</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
            v-if="$hasPermission('ENT_MCH_INFO_DEL')"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="pagination.current"
      :limit.sync="pagination.size"
      @pagination="getList"
    />

    <!-- IP白名单弹窗 -->
    <el-dialog title="IP白名单设置" :visible.sync="whiteIpVisible" width="600px">
      <el-form :model="whiteIpForm" label-width="120px">
        <el-form-item label="API白名单">
          <el-input
            v-model="whiteIpForm.apiWhiteIp"
            type="textarea"
            :rows="4"
            placeholder="请输入API白名单IP，多个IP用逗号分隔"
          />
        </el-form-item>
        <el-form-item label="登录白名单">
          <el-input
            v-model="whiteIpForm.loginWhiteIp"
            type="textarea"
            :rows="4"
            placeholder="请输入登录白名单IP，多个IP用逗号分隔"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="whiteIpVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveWhiteIp">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 对应后端API: MchInfoController
// 主要接口: /api/mchInfo (GET, POST, PUT, DELETE)
import { getMerchantList, deleteMerchant, getWhiteIp, setWhiteIp, resetGoogleAuth } from '@/api/merchant'
import Pagination from '@/components/Pagination'

export default {
  name: 'MerchantList',
  components: {
    Pagination
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      searchForm: {
        mchNo: '',
        mchName: '',
        isvNo: '',
        type: null,
        state: null
      },
      pagination: {
        current: 1,
        size: 10
      },
      selectedRows: [],
      whiteIpVisible: false,
      whiteIpForm: {
        apiWhiteIp: '',
        loginWhiteIp: ''
      },
      currentMchNo: ''
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取商户列表
    async getList() {
      this.loading = true
      try {
        const params = {
          ...this.searchForm,
          current: this.pagination.current,
          size: this.pagination.size
        }
        const response = await getMerchantList(params)
        if (response.code === 0) {
          this.tableData = response.data.records
          this.total = response.data.total
        } else {
          this.$message.error(response.msg || '获取数据失败')
        }
      } catch (error) {
        this.$message.error('获取数据失败')
        console.error(error)
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.getList()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        mchNo: '',
        mchName: '',
        isvNo: '',
        type: null,
        state: null
      }
      this.pagination.current = 1
      this.getList()
    },

    // 新增商户
    handleAdd() {
      this.$router.push('/merchant/add')
    },

    // 查看商户详情
    handleView(row) {
      this.$router.push(`/merchant/detail/${row.mchNo}`)
    },

    // 编辑商户
    handleEdit(row) {
      this.$router.push(`/merchant/edit/${row.mchNo}`)
    },

    // IP白名单设置
    async handleWhiteIp(row) {
      this.currentMchNo = row.mchNo
      try {
        const response = await getWhiteIp(row.mchNo)
        if (response.code === 0) {
          this.whiteIpForm = {
            apiWhiteIp: response.data.apiWhiteIp || '',
            loginWhiteIp: response.data.loginWhiteIp || ''
          }
          this.whiteIpVisible = true
        } else {
          this.$message.error(response.msg || '获取白名单失败')
        }
      } catch (error) {
        this.$message.error('获取白名单失败')
        console.error(error)
      }
    },

    // 保存IP白名单
    async handleSaveWhiteIp() {
      try {
        const response = await setWhiteIp(this.currentMchNo, this.whiteIpForm)
        if (response.code === 0) {
          this.$message.success('设置成功')
          this.whiteIpVisible = false
        } else {
          this.$message.error(response.msg || '设置失败')
        }
      } catch (error) {
        this.$message.error('设置失败')
        console.error(error)
      }
    },

    // 删除商户
    handleDelete(row) {
      this.$confirm(`确定要删除商户 ${row.mchName} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteMerchant(row.mchNo)
          if (response.code === 0) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(response.msg || '删除失败')
          }
        } catch (error) {
          this.$message.error('删除失败')
          console.error(error)
        }
      })
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-bottom: 10px;
}
</style>