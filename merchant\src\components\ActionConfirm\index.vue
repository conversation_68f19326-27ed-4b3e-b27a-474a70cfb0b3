<template>
  <span>
    <slot :confirm="handleConfirm" :loading="loading">
      <el-button
        :type="buttonType"
        :size="buttonSize"
        :loading="loading"
        :disabled="disabled"
        @click="handleConfirm"
      >
        {{ buttonText }}
      </el-button>
    </slot>
  </span>
</template>

<script>
export default {
  name: 'ActionConfirm',
  props: {
    // 确认消息
    message: {
      type: String,
      required: true
    },
    
    // 确认标题
    title: {
      type: String,
      default: '确认操作'
    },
    
    // 确认类型
    type: {
      type: String,
      default: 'warning',
      validator: value => ['success', 'info', 'warning', 'error'].includes(value)
    },
    
    // 确认按钮文本
    confirmButtonText: {
      type: String,
      default: '确定'
    },
    
    // 取消按钮文本
    cancelButtonText: {
      type: String,
      default: '取消'
    },
    
    // 按钮类型
    buttonType: {
      type: String,
      default: 'primary'
    },
    
    // 按钮尺寸
    buttonSize: {
      type: String,
      default: 'medium'
    },
    
    // 按钮文本
    buttonText: {
      type: String,
      default: '操作'
    },
    
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    
    // 是否显示加载状态
    showLoading: {
      type: Boolean,
      default: true
    },
    
    // 确认后是否自动执行操作
    autoExecute: {
      type: Boolean,
      default: true
    },
    
    // 操作函数
    action: {
      type: Function,
      default: null
    },
    
    // 成功消息
    successMessage: {
      type: String,
      default: '操作成功'
    },
    
    // 错误消息
    errorMessage: {
      type: String,
      default: '操作失败'
    }
  },
  
  data() {
    return {
      loading: false
    }
  },
  
  methods: {
    async handleConfirm() {
      if (this.disabled || this.loading) {
        return
      }
      
      try {
        // 显示确认对话框
        await this.$confirm(this.message, this.title, {
          confirmButtonText: this.confirmButtonText,
          cancelButtonText: this.cancelButtonText,
          type: this.type,
          center: false
        })
        
        // 触发确认事件
        this.$emit('confirm')
        
        // 自动执行操作
        if (this.autoExecute && this.action) {
          await this.executeAction()
        }
        
      } catch (error) {
        // 用户取消操作
        this.$emit('cancel')
      }
    },
    
    async executeAction() {
      if (!this.action || typeof this.action !== 'function') {
        return
      }
      
      try {
        if (this.showLoading) {
          this.loading = true
        }
        
        const result = await this.action()
        
        // 显示成功消息
        if (this.successMessage) {
          this.$message.success(this.successMessage)
        }
        
        // 触发成功事件
        this.$emit('success', result)
        
        return result
        
      } catch (error) {
        // 显示错误消息
        if (this.errorMessage) {
          this.$message.error(error.message || this.errorMessage)
        }
        
        // 触发错误事件
        this.$emit('error', error)
        
        throw error
        
      } finally {
        this.loading = false
      }
    }
  }
}
</script>