import { asyncRoutes, constantRoutes } from '@/router'
import { filterRoutes } from '@/utils/permission'

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES(state, routes) {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  generateRoutes({ commit, rootGetters }) {
    return new Promise(resolve => {
      const userInfo = rootGetters['user/userInfo']
      let accessedRoutes
      
      // 如果是超级管理员，拥有所有权限
      if (userInfo.roles && userInfo.roles.includes('admin')) {
        accessedRoutes = asyncRoutes || []
      } else {
        // 根据用户权限过滤路由
        accessedRoutes = filterRoutes(asyncRoutes, userInfo)
      }
      
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  }
}

const getters = {
  routes: state => state.routes,
  addRoutes: state => state.addRoutes
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}