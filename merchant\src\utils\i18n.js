/**
 * 国际化工具函数
 */

/**
 * 获取当前语言
 * @returns {string} 当前语言代码
 */
export function getCurrentLanguage() {
  return localStorage.getItem('language') || 'zh'
}

/**
 * 设置语言
 * @param {string} language 语言代码
 */
export function setLanguage(language) {
  localStorage.setItem('language', language)
}

/**
 * 获取支持的语言列表
 * @returns {Array} 支持的语言列表
 */
export function getSupportedLanguages() {
  return [
    { code: 'zh', name: '中文', nativeName: '中文' },
    { code: 'en', name: 'English', nativeName: 'English' }
  ]
}

/**
 * 检查是否为支持的语言
 * @param {string} language 语言代码
 * @returns {boolean} 是否支持
 */
export function isSupportedLanguage(language) {
  const supportedLanguages = getSupportedLanguages()
  return supportedLanguages.some(lang => lang.code === language)
}

/**
 * 获取浏览器默认语言
 * @returns {string} 浏览器语言代码
 */
export function getBrowserLanguage() {
  const language = navigator.language || navigator.userLanguage
  if (language.startsWith('zh')) {
    return 'zh'
  } else if (language.startsWith('en')) {
    return 'en'
  }
  return 'zh' // 默认中文
}

/**
 * 格式化日期时间（根据当前语言）
 * @param {Date|string} date 日期
 * @param {string} format 格式类型 'date' | 'datetime' | 'time'
 * @returns {string} 格式化后的日期时间
 */
export function formatDateTime(date, format = 'datetime') {
  if (!date) return ''
  
  const dateObj = new Date(date)
  const language = getCurrentLanguage()
  
  const options = {
    date: { year: 'numeric', month: '2-digit', day: '2-digit' },
    datetime: { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    },
    time: { hour: '2-digit', minute: '2-digit', second: '2-digit' }
  }
  
  const locale = language === 'zh' ? 'zh-CN' : 'en-US'
  return dateObj.toLocaleString(locale, options[format])
}

/**
 * 格式化数字（根据当前语言）
 * @param {number} number 数字
 * @param {object} options 格式化选项
 * @returns {string} 格式化后的数字
 */
export function formatNumber(number, options = {}) {
  if (number === null || number === undefined) return ''
  
  const language = getCurrentLanguage()
  const locale = language === 'zh' ? 'zh-CN' : 'en-US'
  
  return new Intl.NumberFormat(locale, options).format(number)
}

/**
 * 格式化货币（根据当前语言）
 * @param {number} amount 金额
 * @param {string} currency 货币代码
 * @returns {string} 格式化后的货币
 */
export function formatCurrency(amount, currency = 'USD') {
  if (amount === null || amount === undefined) return ''
  
  const language = getCurrentLanguage()
  const locale = language === 'zh' ? 'zh-CN' : 'en-US'
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency
  }).format(amount)
}