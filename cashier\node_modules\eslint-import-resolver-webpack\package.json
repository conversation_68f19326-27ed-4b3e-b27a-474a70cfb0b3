{"name": "eslint-import-resolver-webpack", "version": "0.13.10", "description": "Resolve paths to dependencies, given a webpack.config.js. Plugin for eslint-plugin-import.", "main": "index.js", "scripts": {"prepublishOnly": "cp ../../{LICENSE,.npmrc} ./", "tests-only": "nyc mocha -t 5s", "test": "npm run tests-only"}, "files": ["index.js", "config.js"], "repository": {"type": "git", "url": "git+https://github.com/import-js/eslint-plugin-import.git", "directory": "resolvers/webpack"}, "keywords": ["eslint-plugin-import", "eslint", "jsnext", "modules", "webpack"], "author": "<PERSON> (<EMAIL>)", "license": "MIT", "bugs": {"url": "https://github.com/import-js/eslint-plugin-import/issues"}, "homepage": "https://github.com/import-js/eslint-plugin-import/tree/HEAD/resolvers/webpack", "dependencies": {"debug": "^3.2.7", "enhanced-resolve": "^0.9.1", "find-root": "^1.1.0", "hasown": "^2.0.2", "interpret": "^1.4.0", "is-core-module": "^2.15.1", "is-regex": "^1.2.0", "lodash": "^4.17.21", "resolve": "^2.0.0-next.5", "semver": "^5.7.2"}, "peerDependencies": {"eslint-plugin-import": ">=1.4.0", "webpack": ">=1.11.0"}, "devDependencies": {"babel-plugin-istanbul": "^4.1.6", "babel-register": "^6.26.0", "chai": "^3.5.0", "mocha": "^3.5.3", "nyc": "^11.9.0", "webpack": "https://gist.github.com/ljharb/9cdb687f3806f8e6cb8a365d0b7840eb"}, "engines": {"node": ">= 6"}}