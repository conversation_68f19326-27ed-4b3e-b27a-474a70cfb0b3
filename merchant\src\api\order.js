import request from '@/utils/request'

// 获取订单列表
export function getOrderList(params) {
  return request({
    url: '/api/order/list',
    method: 'get',
    params: {
      page: params.page || 1,
      size: params.size || 20,
      order_id: params.order_id,
      status: params.status,
      created_at_start: params.created_at_start,
      created_at_end: params.created_at_end,
      mch_no: params.mch_no
    }
  })
}

// 获取订单详情
export function getOrderDetail(orderId) {
  return request({
    url: `/api/order/detail/${orderId}`,
    method: 'get'
  })
}

// 导出订单
export function exportOrders(params) {
  return request({
    url: '/api/order/export',
    method: 'post',
    data: {
      order_id: params.order_id,
      status: params.status,
      created_at_start: params.created_at_start,
      created_at_end: params.created_at_end,
      mch_no: params.mch_no
    },
    responseType: 'blob'
  })
}

// 获取订单统计
export function getOrderStats(params) {
  return request({
    url: '/api/order/stats',
    method: 'get',
    params: {
      date_start: params.date_start,
      date_end: params.date_end
    }
  })
}

// 订单状态枚举
export const ORDER_STATUS = {
  0: { text: '待支付', type: 'warning' },
  1: { text: '支付中', type: 'info' },
  2: { text: '支付成功', type: 'success' },
  3: { text: '支付失败', type: 'danger' },
  4: { text: '已取消', type: 'info' },
  5: { text: '已退款', type: 'warning' }
}

// 获取订单状态选项
export function getOrderStatusOptions() {
  return Object.keys(ORDER_STATUS).map(key => ({
    value: parseInt(key),
    label: ORDER_STATUS[key].text
  }))
}