<template>
  <div class="menu-item">
    <i v-if="icon" :class="icon" class="menu-icon"></i>
    <span class="menu-title">{{ title }}</span>
  </div>
</template>

<script>
export default {
  name: 'MenuItem',
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped>
.menu-item {
  display: flex;
  align-items: center;
}

.menu-icon {
  margin-right: 8px;
  font-size: 16px;
}

.menu-title {
  font-size: 14px;
}
</style>