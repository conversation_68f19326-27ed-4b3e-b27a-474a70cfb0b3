{"name": "eslint-config-standard", "description": "JavaScript Standard Style - ESLint Shareable Config", "version": "16.0.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/standard/eslint-config-standard/issues"}, "devDependencies": {"eslint": "^7.12.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "tape": "^5.0.1"}, "homepage": "https://github.com/standard/eslint-config-standard", "keywords": ["JavaScript Standard Style", "check", "checker", "code", "code checker", "code linter", "code standards", "code style", "enforce", "eslint", "eslintconfig", "hint", "jscs", "j<PERSON>t", "lint", "policy", "quality", "simple", "standard", "standard style", "style", "style checker", "style linter", "verify"], "license": "MIT", "main": "index.js", "peerDependencies": {"eslint": "^7.12.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1 || ^5.0.0"}, "repository": {"type": "git", "url": "git://github.com/standard/eslint-config-standard.git"}, "scripts": {"lint": "eslint . --config=eslintrc.json", "test": "npm run lint && tape test/*.js"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}