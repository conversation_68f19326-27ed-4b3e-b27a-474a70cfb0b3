/**
 * 加载状态管理工具
 */

import { Loading } from 'element-ui'

class LoadingManager {
  constructor() {
    this.loadingInstances = new Map()
    this.globalLoading = null
    this.loadingCount = 0
  }

  // 显示全局加载
  showGlobal(options = {}) {
    if (this.globalLoading) {
      return this.globalLoading
    }

    const defaultOptions = {
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    }

    this.globalLoading = Loading.service({
      ...defaultOptions,
      ...options
    })

    return this.globalLoading
  }

  // 隐藏全局加载
  hideGlobal() {
    if (this.globalLoading) {
      this.globalLoading.close()
      this.globalLoading = null
    }
  }

  // 显示元素加载
  show(target, options = {}) {
    const key = typeof target === 'string' ? target : target.$el || target

    if (this.loadingInstances.has(key)) {
      return this.loadingInstances.get(key)
    }

    const defaultOptions = {
      target: key,
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(255, 255, 255, 0.9)'
    }

    const loading = Loading.service({
      ...defaultOptions,
      ...options
    })

    this.loadingInstances.set(key, loading)
    return loading
  }

  // 隐藏元素加载
  hide(target) {
    const key = typeof target === 'string' ? target : target.$el || target
    const loading = this.loadingInstances.get(key)

    if (loading) {
      loading.close()
      this.loadingInstances.delete(key)
    }
  }

  // 隐藏所有加载
  hideAll() {
    this.hideGlobal()
    this.loadingInstances.forEach(loading => loading.close())
    this.loadingInstances.clear()
  }

  // 自动管理加载状态
  auto(promise, target = null, options = {}) {
    const loading = target ? this.show(target, options) : this.showGlobal(options)

    return promise
      .finally(() => {
        if (target) {
          this.hide(target)
        } else {
          this.hideGlobal()
        }
      })
  }

  // 批量加载管理
  batch(promises, target = null, options = {}) {
    const loading = target ? this.show(target, options) : this.showGlobal(options)

    return Promise.allSettled(promises)
      .finally(() => {
        if (target) {
          this.hide(target)
        } else {
          this.hideGlobal()
        }
      })
  }
}

// 创建全局实例
export const loadingManager = new LoadingManager()

// Vue 插件
export const LoadingPlugin = {
  install(Vue) {
    // 添加到 Vue 原型
    Vue.prototype.$loading = loadingManager

    // 全局混入
    Vue.mixin({
      data() {
        return {
          loading: false,
          loadingText: '加载中...'
        }
      },
      methods: {
        // 设置加载状态
        setLoading(loading, text = '加载中...') {
          this.loading = loading
          this.loadingText = text
        },

        // 异步操作包装器
        async withLoading(asyncFn, target = null, options = {}) {
          try {
            this.setLoading(true, options.text)
            return await loadingManager.auto(asyncFn(), target, options)
          } catch (error) {
            console.error('异步操作失败:', error)
            throw error
          } finally {
            this.setLoading(false)
          }
        },

        // 表单提交加载
        async submitWithLoading(submitFn, formRef = 'form') {
          if (this.loading) return

          try {
            // 验证表单
            if (formRef && this.$refs[formRef]) {
              await this.$refs[formRef].validate()
            }

            this.setLoading(true, '提交中...')
            await submitFn()
            
            this.$message.success('操作成功')
          } catch (error) {
            if (error.message !== 'cancel') {
              this.$message.error(error.message || '操作失败')
            }
            throw error
          } finally {
            this.setLoading(false)
          }
        }
      }
    })

    // 指令
    Vue.directive('loading', {
      bind(el, binding) {
        const instance = Loading.service({
          target: el,
          lock: true,
          text: binding.value?.text || '加载中...',
          spinner: binding.value?.spinner || 'el-icon-loading',
          background: binding.value?.background || 'rgba(255, 255, 255, 0.9)'
        })
        el._loadingInstance = instance
      },
      update(el, binding) {
        if (binding.value !== binding.oldValue) {
          if (binding.value) {
            if (!el._loadingInstance) {
              const instance = Loading.service({
                target: el,
                lock: true,
                text: binding.value?.text || '加载中...',
                spinner: binding.value?.spinner || 'el-icon-loading',
                background: binding.value?.background || 'rgba(255, 255, 255, 0.9)'
              })
              el._loadingInstance = instance
            }
          } else {
            if (el._loadingInstance) {
              el._loadingInstance.close()
              el._loadingInstance = null
            }
          }
        }
      },
      unbind(el) {
        if (el._loadingInstance) {
          el._loadingInstance.close()
          el._loadingInstance = null
        }
      }
    })
  }
}

// 装饰器模式的加载管理
export function withLoading(target = null, options = {}) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function(...args) {
      try {
        const loading = target ? loadingManager.show(target, options) : loadingManager.showGlobal(options)
        const result = await originalMethod.apply(this, args)
        return result
      } finally {
        if (target) {
          loadingManager.hide(target)
        } else {
          loadingManager.hideGlobal()
        }
      }
    }

    return descriptor
  }
}

// 防抖加载
export function debounceLoading(delay = 300) {
  let timer = null
  
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value

    descriptor.value = function(...args) {
      if (timer) {
        clearTimeout(timer)
      }

      timer = setTimeout(async () => {
        try {
          loadingManager.showGlobal()
          await originalMethod.apply(this, args)
        } finally {
          loadingManager.hideGlobal()
          timer = null
        }
      }, delay)
    }

    return descriptor
  }
}

export default {
  LoadingManager,
  loadingManager,
  LoadingPlugin,
  withLoading,
  debounceLoading
}