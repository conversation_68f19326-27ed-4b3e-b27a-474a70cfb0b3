/**
 * 商户管理API服务
 * 对应后端Controller: MchInfoController
 * 路由前缀: /api/mchInfo
 */
import request from '@/utils/request'

// 商户信息列表
export function getMerchantList(params) {
  return request({
    url: '/api/mchInfo',
    method: 'get',
    params
  })
}

// 新增商户信息
export function addMerchant(data) {
  return request({
    url: '/api/mchInfo',
    method: 'post',
    data
  })
}

// 查询商户详情
export function getMerchantDetail(mchNo) {
  return request({
    url: `/api/mchInfo/${mchNo}`,
    method: 'get'
  })
}

// 更新商户信息
export function updateMerchant(mchNo, data) {
  return request({
    url: `/api/mchInfo/${mchNo}`,
    method: 'put',
    data
  })
}

// 删除商户信息
export function deleteMerchant(mchNo) {
  return request({
    url: `/api/mchInfo/${mchNo}`,
    method: 'delete'
  })
}

// 获取IP白名单
export function getWhiteIp(mchNo) {
  return request({
    url: `/api/mchInfo/getWhiteIp/${mchNo}`,
    method: 'get'
  })
}

// 设置IP白名单
export function setWhiteIp(mchNo, data) {
  return request({
    url: `/api/mchInfo/whiteIp/${mchNo}`,
    method: 'put',
    data
  })
}

// 重置Google验证码绑定
export function resetGoogleAuth(mchNo) {
  return request({
    url: `/api/mchInfo/resetGoogleVertif/${mchNo}`,
    method: 'put'
  })
}