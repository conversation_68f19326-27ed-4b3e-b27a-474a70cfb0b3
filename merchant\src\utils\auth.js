import store from '@/store'
import { hasPermission, hasRole } from './permission'

/**
 * 权限管理工具类
 */
export class AuthManager {
  /**
   * 检查多个权限（AND关系）
   * @param {Array} permissions 权限数组
   * @returns {Boolean}
   */
  static hasAllPermissions(permissions) {
    if (!Array.isArray(permissions)) {
      return false
    }
    return permissions.every(permission => hasPermission(permission))
  }

  /**
   * 检查多个权限（OR关系）
   * @param {Array} permissions 权限数组
   * @returns {Boolean}
   */
  static hasAnyPermission(permissions) {
    if (!Array.isArray(permissions)) {
      return hasPermission(permissions)
    }
    return permissions.some(permission => hasPermission(permission))
  }

  /**
   * 检查多个角色（AND关系）
   * @param {Array} roles 角色数组
   * @returns {Boolean}
   */
  static hasAllRoles(roles) {
    if (!Array.isArray(roles)) {
      return false
    }
    return roles.every(role => hasRole(role))
  }

  /**
   * 检查多个角色（OR关系）
   * @param {Array} roles 角色数组
   * @returns {Boolean}
   */
  static hasAnyRole(roles) {
    if (!Array.isArray(roles)) {
      return hasRole(roles)
    }
    return roles.some(role => hasRole(role))
  }

  /**
   * 检查复合权限条件
   * @param {Object} condition 权限条件对象
   * @param {Array} condition.permissions 权限数组
   * @param {Array} condition.roles 角色数组
   * @param {String} condition.logic 逻辑关系 'AND' | 'OR'
   * @returns {Boolean}
   */
  static checkComplexPermission(condition) {
    const { permissions = [], roles = [], logic = 'AND' } = condition
    
    let permissionResult = true
    let roleResult = true
    
    if (permissions.length > 0) {
      permissionResult = logic === 'AND' 
        ? this.hasAllPermissions(permissions)
        : this.hasAnyPermission(permissions)
    }
    
    if (roles.length > 0) {
      roleResult = logic === 'AND'
        ? this.hasAllRoles(roles)
        : this.hasAnyRole(roles)
    }
    
    return logic === 'AND' 
      ? permissionResult && roleResult
      : permissionResult || roleResult
  }

  /**
   * 获取当前用户信息
   * @returns {Object}
   */
  static getCurrentUser() {
    return store.getters['user/userInfo']
  }

  /**
   * 获取当前用户权限
   * @returns {Array}
   */
  static getCurrentPermissions() {
    return store.getters['user/permissions']
  }

  /**
   * 检查是否为超级管理员
   * @returns {Boolean}
   */
  static isSuperAdmin() {
    return hasRole('admin')
  }

  /**
   * 检查是否已登录
   * @returns {Boolean}
   */
  static isLoggedIn() {
    return store.getters['user/isLoggedIn']
  }
}

export default AuthManager