import request from '@/utils/request'

// 登录接口
export function login(data) {
  return request({
    url: '/api/merchant/auth/login',
    method: 'post',
    data: {
      username: data.username,
      password: data.password
    }
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/api/merchant/auth/userinfo',
    method: 'get'
  })
}

// 刷新token
export function refreshToken() {
  return request({
    url: '/api/merchant/auth/refresh',
    method: 'post'
  })
}

// 登出
export function logout() {
  return request({
    url: '/api/merchant/auth/logout',
    method: 'post'
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/api/merchant/auth/change-password',
    method: 'post',
    data: {
      old_password: data.oldPassword,
      new_password: data.newPassword
    }
  })
}