/**
 * Performance monitoring utilities
 */

// Performance metrics collection
class PerformanceMonitor {
  constructor() {
    this.metrics = {}
    this.observers = []
    this.init()
  }

  init() {
    // Monitor page load performance
    if (typeof window !== 'undefined' && window.performance) {
      this.collectPageLoadMetrics()
    }

    // Monitor resource loading
    this.observeResourceTiming()

    // Monitor long tasks
    this.observeLongTasks()
  }

  collectPageLoadMetrics() {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0]
        if (navigation) {
          this.metrics.pageLoad = {
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
            firstPaint: this.getFirstPaint(),
            firstContentfulPaint: this.getFirstContentfulPaint(),
            timeToInteractive: this.getTimeToInteractive()
          }
          this.reportMetrics('pageLoad', this.metrics.pageLoad)
        }
      }, 0)
    })
  }

  getFirstPaint() {
    const paintEntries = performance.getEntriesByType('paint')
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint')
    return firstPaint ? firstPaint.startTime : null
  }

  getFirstContentfulPaint() {
    const paintEntries = performance.getEntriesByType('paint')
    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    return fcp ? fcp.startTime : null
  }

  getTimeToInteractive() {
    // Simplified TTI calculation
    return performance.now()
  }

  observeResourceTiming() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          if (entry.duration > 1000) { // Resources taking more than 1s
            console.warn(`Slow resource: ${entry.name} took ${entry.duration}ms`)
          }
        })
      })
      observer.observe({ entryTypes: ['resource'] })
      this.observers.push(observer)
    }
  }

  observeLongTasks() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach(entry => {
          console.warn(`Long task detected: ${entry.duration}ms`)
          this.reportMetrics('longTask', {
            duration: entry.duration,
            startTime: entry.startTime
          })
        })
      })
      observer.observe({ entryTypes: ['longtask'] })
      this.observers.push(observer)
    }
  }

  reportMetrics(type, data) {
    // In production, send to analytics service
    if (process.env.NODE_ENV === 'development') {
      console.log(`Performance metric [${type}]:`, data)
    }
    
    // Store metrics for later analysis
    if (!this.metrics[type]) {
      this.metrics[type] = []
    }
    this.metrics[type].push({
      timestamp: Date.now(),
      data
    })
  }

  getMetrics() {
    return this.metrics
  }

  disconnect() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// Debounce function for performance optimization
export function debounce(func, wait, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func.apply(this, args)
  }
}

// Throttle function for performance optimization
export function throttle(func, limit) {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Lazy loading utility for images
export function lazyLoadImages() {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target
          img.src = img.dataset.src
          img.classList.remove('lazy')
          img.classList.add('loaded')
          observer.unobserve(img)
        }
      })
    })

    const lazyImages = document.querySelectorAll('img[data-src]')
    lazyImages.forEach(img => imageObserver.observe(img))
  } else {
    // Fallback for browsers without IntersectionObserver
    const lazyImages = document.querySelectorAll('img[data-src]')
    lazyImages.forEach(img => {
      img.src = img.dataset.src
      img.classList.remove('lazy')
      img.classList.add('loaded')
    })
  }
}

// Memory usage monitoring
export function monitorMemoryUsage() {
  if ('memory' in performance) {
    const memory = performance.memory
    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit * 100).toFixed(2)
    }
  }
  return null
}

// Bundle size analyzer helper
export function analyzeBundleSize() {
  if (process.env.NODE_ENV === 'development') {
    // This would integrate with webpack-bundle-analyzer in a real setup
    console.log('Bundle analysis would be performed here')
  }
}

// Cache management utilities
export class CacheManager {
  constructor(maxSize = 100) {
    this.cache = new Map()
    this.maxSize = maxSize
  }

  set(key, value, ttl = 300000) { // 5 minutes default TTL
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl
    })
  }

  get(key) {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.value
  }

  clear() {
    this.cache.clear()
  }

  size() {
    return this.cache.size
  }
}

// Initialize performance monitoring
export const performanceMonitor = new PerformanceMonitor()

// Export cache manager instance
export const cacheManager = new CacheManager()

export default {
  performanceMonitor,
  debounce,
  throttle,
  lazyLoadImages,
  monitorMemoryUsage,
  analyzeBundleSize,
  CacheManager,
  cacheManager
}