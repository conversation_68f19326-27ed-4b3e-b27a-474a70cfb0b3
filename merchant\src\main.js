import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import i18n from './i18n'
import './utils/request'
import './utils/errorHandler'
import permission from '@/directive/permission'
import Permission from '@/components/Permission'

Vue.config.productionTip = false

Vue.use(ElementUI)
Vue.use(permission)
Vue.component('Permission', Permission)

new Vue({
  router,
  store,
  i18n,
  render: h => h(App)
}).$mount('#app')