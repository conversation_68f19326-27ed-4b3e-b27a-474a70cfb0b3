import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import { formatDateTime, formatCurrency, getCurrentLanguage } from './i18n'
import i18n from '@/i18n'

/**
 * Excel导出工具类
 */
class ExcelExporter {
  /**
   * 导出数据到Excel
   * @param {Array} data 数据数组
   * @param {Array} columns 列配置
   * @param {string} filename 文件名
   * @param {object} options 导出选项
   */
  static exportToExcel(data, columns, filename = 'export', options = {}) {
    try {
      // 处理数据
      const processedData = this.processData(data, columns)
      
      // 创建工作簿
      const workbook = XLSX.utils.book_new()
      
      // 创建工作表
      const worksheet = XLSX.utils.json_to_sheet(processedData, {
        header: columns.map(col => col.title || col.label),
        skipHeader: false
      })
      
      // 设置列宽
      if (options.columnWidths) {
        worksheet['!cols'] = options.columnWidths.map(width => ({ wch: width }))
      } else {
        // 自动计算列宽
        worksheet['!cols'] = this.calculateColumnWidths(processedData, columns)
      }
      
      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, options.sheetName || 'Sheet1')
      
      // 生成Excel文件
      const excelBuffer = XLSX.write(workbook, {
        bookType: 'xlsx',
        type: 'array'
      })
      
      // 保存文件
      const blob = new Blob([excelBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      
      const finalFilename = `${filename}_${this.getTimestamp()}.xlsx`
      saveAs(blob, finalFilename)
      
      return true
    } catch (error) {
      console.error('Excel export error:', error)
      throw new Error(i18n.t('common.exportFailed') || '导出失败')
    }
  }
  
  /**
   * 处理数据
   * @param {Array} data 原始数据
   * @param {Array} columns 列配置
   * @returns {Array} 处理后的数据
   */
  static processData(data, columns) {
    return data.map(row => {
      const processedRow = {}
      
      columns.forEach(column => {
        const { key, title, label, formatter, type } = column
        const columnTitle = title || label
        let value = this.getNestedValue(row, key)
        
        // 应用格式化器
        if (formatter && typeof formatter === 'function') {
          value = formatter(value, row)
        } else if (type) {
          value = this.formatByType(value, type)
        }
        
        processedRow[columnTitle] = value
      })
      
      return processedRow
    })
  }
  
  /**
   * 获取嵌套对象的值
   * @param {object} obj 对象
   * @param {string} path 路径
   * @returns {any} 值
   */
  static getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : ''
    }, obj)
  }
  
  /**
   * 根据类型格式化值
   * @param {any} value 值
   * @param {string} type 类型
   * @returns {string} 格式化后的值
   */
  static formatByType(value, type) {
    if (value === null || value === undefined) return ''
    
    switch (type) {
      case 'date':
        return formatDateTime(value, 'date')
      case 'datetime':
        return formatDateTime(value, 'datetime')
      case 'time':
        return formatDateTime(value, 'time')
      case 'currency':
        return formatCurrency(value)
      case 'number':
        return typeof value === 'number' ? value.toString() : value
      case 'status':
        return this.formatStatus(value)
      default:
        return value.toString()
    }
  }
  
  /**
   * 格式化状态值
   * @param {any} value 状态值
   * @returns {string} 格式化后的状态
   */
  static formatStatus(value) {
    const statusMap = {
      0: i18n.t('common.disabled') || '禁用',
      1: i18n.t('common.enabled') || '启用',
      pending: i18n.t('merchant.pending') || '待审核',
      approved: i18n.t('merchant.approved') || '已通过',
      rejected: i18n.t('merchant.rejected') || '已拒绝'
    }
    
    return statusMap[value] || value.toString()
  }
  
  /**
   * 计算列宽
   * @param {Array} data 数据
   * @param {Array} columns 列配置
   * @returns {Array} 列宽数组
   */
  static calculateColumnWidths(data, columns) {
    return columns.map((column, index) => {
      const columnTitle = column.title || column.label
      let maxLength = columnTitle.length
      
      // 计算数据中最长的内容
      data.forEach(row => {
        const value = row[columnTitle]
        if (value) {
          const length = value.toString().length
          if (length > maxLength) {
            maxLength = length
          }
        }
      })
      
      // 设置最小和最大宽度
      const minWidth = 10
      const maxWidth = 50
      const calculatedWidth = Math.min(Math.max(maxLength + 2, minWidth), maxWidth)
      
      return { wch: calculatedWidth }
    })
  }
  
  /**
   * 获取时间戳
   * @returns {string} 时间戳字符串
   */
  static getTimestamp() {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')
    
    return `${year}${month}${day}_${hours}${minutes}${seconds}`
  }
  
  /**
   * 导出商户数据
   * @param {Array} merchants 商户数据
   * @param {string} filename 文件名
   */
  static exportMerchants(merchants, filename = 'merchants') {
    const columns = [
      { key: 'mch_no', title: i18n.t('merchant.merchantNo') || '商户号' },
      { key: 'mch_name', title: i18n.t('merchant.merchantName') || '商户名称' },
      { key: 'contact_name', title: i18n.t('merchant.contactName') || '联系人' },
      { key: 'contact_tel', title: i18n.t('merchant.contactPhone') || '联系电话' },
      { key: 'state', title: i18n.t('merchant.state') || '审核状态', type: 'status' },
      { key: 'created_at', title: i18n.t('common.createTime') || '创建时间', type: 'datetime' }
    ]
    
    return this.exportToExcel(merchants, columns, filename)
  }
  
  /**
   * 导出订单数据
   * @param {Array} orders 订单数据
   * @param {string} filename 文件名
   */
  static exportOrders(orders, filename = 'orders') {
    const columns = [
      { key: 'order_id', title: i18n.t('order.orderId') || '订单号' },
      { key: 'mch_no', title: i18n.t('order.merchantNo') || '商户号' },
      { key: 'amount', title: i18n.t('order.amount') || '订单金额', type: 'currency' },
      { key: 'currency', title: i18n.t('order.currency') || '货币类型' },
      { key: 'status', title: i18n.t('order.status') || '订单状态', type: 'status' },
      { key: 'created_at', title: i18n.t('common.createTime') || '创建时间', type: 'datetime' }
    ]
    
    return this.exportToExcel(orders, columns, filename)
  }
  
  /**
   * 导出结算数据
   * @param {Array} settlements 结算数据
   * @param {string} filename 文件名
   */
  static exportSettlements(settlements, filename = 'settlements') {
    const columns = [
      { key: 'settle_id', title: i18n.t('settlement.settleId') || '结算ID' },
      { key: 'mch_no', title: i18n.t('settlement.merchantNo') || '商户号' },
      { key: 'amount', title: i18n.t('settlement.amount') || '结算金额', type: 'currency' },
      { key: 'status', title: i18n.t('settlement.status') || '结算状态', type: 'status' },
      { key: 'created_at', title: i18n.t('common.createTime') || '创建时间', type: 'datetime' }
    ]
    
    return this.exportToExcel(settlements, columns, filename)
  }
}

export default ExcelExporter