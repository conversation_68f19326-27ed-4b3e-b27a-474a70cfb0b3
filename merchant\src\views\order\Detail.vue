<template>
  <div class="order-detail">
    <div class="page-header">
      <el-button @click="goBack" icon="el-icon-arrow-left">返回</el-button>
      <div class="header-info">
        <h2>订单详情</h2>
        <p>订单号：{{ orderInfo.order_id }}</p>
      </div>
    </div>
    
    <div v-loading="loading" class="detail-content">
      <!-- 订单状态 -->
      <el-card class="status-card">
        <div class="status-header">
          <div class="status-icon">
            <i :class="statusIcon" :style="{ color: statusColor }"></i>
          </div>
          <div class="status-info">
            <h3>{{ getStatusText(orderInfo.status) }}</h3>
            <p class="status-desc">{{ getStatusDesc(orderInfo.status) }}</p>
          </div>
        </div>
        
        <!-- 订单进度 -->
        <div class="progress-section">
          <el-steps :active="currentStep" finish-status="success" align-center>
            <el-step title="订单创建" :description="formatTime(orderInfo.created_at)"></el-step>
            <el-step title="等待支付" :description="orderInfo.status >= 1 ? '用户选择支付方式' : ''"></el-step>
            <el-step title="支付处理" :description="orderInfo.status >= 2 ? '支付处理中' : ''"></el-step>
            <el-step title="支付完成" :description="orderInfo.status === 2 ? formatTime(orderInfo.updated_at) : ''"></el-step>
          </el-steps>
        </div>
      </el-card>
      
      <!-- 基本信息 -->
      <el-card class="info-card">
        <div slot="header">
          <span>基本信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ orderInfo.order_id || '-' }}</el-descriptions-item>
          <el-descriptions-item label="商户号">{{ orderInfo.mch_no || '-' }}</el-descriptions-item>
          <el-descriptions-item label="订单金额">
            <span class="amount-text">¥{{ formatMoney(orderInfo.amount) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="币种">{{ orderInfo.currency || '-' }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(orderInfo.status)">
              {{ getStatusText(orderInfo.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ getPayMethodText(orderInfo.pay_method) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(orderInfo.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatTime(orderInfo.updated_at) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 商品信息 -->
      <el-card class="info-card" v-if="orderInfo.product_name">
        <div slot="header">
          <span>商品信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="商品名称">{{ orderInfo.product_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="商品描述">{{ orderInfo.product_desc || '-' }}</el-descriptions-item>
          <el-descriptions-item label="商品数量">{{ orderInfo.quantity || 1 }}</el-descriptions-item>
          <el-descriptions-item label="单价">¥{{ formatMoney(orderInfo.unit_price) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 支付信息 -->
      <el-card class="info-card" v-if="orderInfo.pay_info">
        <div slot="header">
          <span>支付信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="支付渠道">{{ orderInfo.pay_info.channel_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="支付流水号">{{ orderInfo.pay_info.transaction_id || '-' }}</el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ formatTime(orderInfo.pay_info.pay_time) }}</el-descriptions-item>
          <el-descriptions-item label="手续费">¥{{ formatMoney(orderInfo.pay_info.fee) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 客户信息 -->
      <el-card class="info-card" v-if="orderInfo.customer_info">
        <div slot="header">
          <span>客户信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="客户姓名">{{ orderInfo.customer_info.name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="客户邮箱">{{ orderInfo.customer_info.email || '-' }}</el-descriptions-item>
          <el-descriptions-item label="客户电话">{{ orderInfo.customer_info.phone || '-' }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ orderInfo.customer_info.ip || '-' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 回调信息 -->
      <el-card class="info-card" v-if="orderInfo.notify_info">
        <div slot="header">
          <span>回调信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="回调地址">{{ orderInfo.notify_url || '-' }}</el-descriptions-item>
          <el-descriptions-item label="回调状态">
            <el-tag :type="orderInfo.notify_info.status === 1 ? 'success' : 'danger'">
              {{ orderInfo.notify_info.status === 1 ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="回调次数">{{ orderInfo.notify_info.count || 0 }}</el-descriptions-item>
          <el-descriptions-item label="最后回调时间">{{ formatTime(orderInfo.notify_info.last_time) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 备注信息 -->
      <el-card class="info-card" v-if="orderInfo.remark">
        <div slot="header">
          <span>备注信息</span>
        </div>
        <div class="remark-content">
          {{ orderInfo.remark }}
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getOrderDetail, ORDER_STATUS } from '@/api/order'

export default {
  name: 'OrderDetail',
  data() {
    return {
      loading: false,
      orderInfo: {}
    }
  },
  computed: {
    orderId() {
      return this.$route.params.id
    },
    statusIcon() {
      const iconMap = {
        0: 'el-icon-time',
        1: 'el-icon-loading',
        2: 'el-icon-success',
        3: 'el-icon-error',
        4: 'el-icon-close',
        5: 'el-icon-refresh'
      }
      return iconMap[this.orderInfo.status] || 'el-icon-question'
    },
    statusColor() {
      const colorMap = {
        0: '#E6A23C',
        1: '#409EFF',
        2: '#67C23A',
        3: '#F56C6C',
        4: '#909399',
        5: '#E6A23C'
      }
      return colorMap[this.orderInfo.status] || '#909399'
    },
    currentStep() {
      const stepMap = {
        0: 1, // 待支付
        1: 2, // 支付中
        2: 4, // 支付成功
        3: 3, // 支付失败
        4: 1, // 已取消
        5: 3  // 已退款
      }
      return stepMap[this.orderInfo.status] || 0
    }
  },
  created() {
    this.fetchOrderDetail()
  },
  methods: {
    async fetchOrderDetail() {
      this.loading = true
      try {
        const response = await getOrderDetail(this.orderId)
        if (response.code === 0) {
          this.orderInfo = response.data
        } else {
          this.$message.error(response.msg || '获取订单详情失败')
        }
      } catch (error) {
        console.error('Fetch order detail error:', error)
        this.$message.error('获取订单详情失败')
      } finally {
        this.loading = false
      }
    },
    goBack() {
      this.$router.go(-1)
    },
    getStatusText(status) {
      return ORDER_STATUS[status]?.text || '未知'
    },
    getStatusType(status) {
      return ORDER_STATUS[status]?.type || 'info'
    },
    getStatusDesc(status) {
      const descMap = {
        0: '订单已创建，等待用户支付',
        1: '用户正在支付中，请耐心等待',
        2: '订单支付成功，交易完成',
        3: '订单支付失败，请联系客服',
        4: '订单已被取消',
        5: '订单已退款'
      }
      return descMap[status] || ''
    },
    getPayMethodText(method) {
      const methodMap = {
        1: '外卡支付',
        2: 'USDT支付',
        3: '本地支付'
      }
      return methodMap[method] || '未知'
    },
    formatMoney(amount) {
      return Number(amount || 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.order-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.header-info {
  margin-left: 16px;
}

.header-info h2 {
  color: #333;
  margin: 0 0 4px 0;
}

.header-info p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.status-card {
  margin-bottom: 20px;
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-icon {
  margin-right: 20px;
}

.status-icon i {
  font-size: 48px;
}

.status-info h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 20px;
}

.status-desc {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.progress-section {
  padding: 20px 0;
}

.info-card {
  margin-bottom: 20px;
}

.amount-text {
  font-weight: 600;
  color: #67C23A;
  font-size: 16px;
}

.remark-content {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
  color: #333;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-info {
    margin-left: 0;
    margin-top: 10px;
  }
  
  .status-header {
    flex-direction: column;
    text-align: center;
  }
  
  .status-icon {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style>