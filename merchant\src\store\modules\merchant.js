const state = {
  merchantList: [],
  currentMerchant: {},
  loading: false
}

const mutations = {
  SET_MERCHANT_LIST(state, list) {
    state.merchantList = list
  },
  SET_CURRENT_MERCHANT(state, merchant) {
    state.currentMerchant = merchant
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  }
}

const actions = {
  setMerchantList({ commit }, list) {
    commit('SET_MERCHANT_LIST', list)
  },
  setCurrentMerchant({ commit }, merchant) {
    commit('SET_CURRENT_MERCHANT', merchant)
  },
  setLoading({ commit }, loading) {
    commit('SET_LOADING', loading)
  }
}

const getters = {
  merchantList: state => state.merchantList,
  currentMerchant: state => state.currentMerchant,
  loading: state => state.loading
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}