<template>
  <div class="file-upload">
    <el-upload
      :action="uploadUrl"
      :headers="headers"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-progress="handleProgress"
      :show-file-list="false"
      :disabled="uploading"
      accept="image/*,.pdf"
    >
      <div class="upload-area" :class="{ 'is-uploading': uploading }">
        <div v-if="!fileUrl && !uploading" class="upload-placeholder">
          <i class="el-icon-plus"></i>
          <div class="upload-text">{{ placeholder }}</div>
        </div>
        <div v-else-if="uploading" class="upload-progress">
          <el-progress
            type="circle"
            :percentage="uploadProgress"
            :width="60"
          ></el-progress>
          <div class="progress-text">上传中...</div>
        </div>
        <div v-else class="upload-success">
          <img v-if="isImage" :src="fileUrl" class="uploaded-image" />
          <div v-else class="uploaded-file">
            <i class="el-icon-document"></i>
            <div class="file-name">{{ fileName }}</div>
          </div>
          <div class="upload-actions">
            <el-button size="mini" @click.stop="previewFile">预览</el-button>
            <el-button size="mini" type="danger" @click.stop="removeFile">删除</el-button>
          </div>
        </div>
      </div>
    </el-upload>
    
    <!-- 预览对话框 -->
    <el-dialog
      title="文件预览"
      :visible.sync="previewVisible"
      width="60%"
      center
    >
      <div class="preview-content">
        <img v-if="isImage" :src="fileUrl" style="width: 100%; max-height: 500px; object-fit: contain;" />
        <div v-else class="pdf-preview">
          <p>PDF文件预览</p>
          <a :href="fileUrl" target="_blank" class="preview-link">点击查看完整文件</a>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'FileUpload',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '点击上传文件'
    },
    maxSize: {
      type: Number,
      default: 5 // MB
    }
  },
  data() {
    return {
      uploading: false,
      uploadProgress: 0,
      fileUrl: this.value,
      fileName: '',
      previewVisible: false
    }
  },
  computed: {
    uploadUrl() {
      return process.env.VUE_APP_BASE_API + '/api/upload'
    },
    headers() {
      return {
        'Authorization': 'Bearer ' + this.$store.getters.token
      }
    },
    isImage() {
      if (!this.fileUrl) return false
      return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(this.fileUrl)
    }
  },
  watch: {
    value(newVal) {
      this.fileUrl = newVal
    }
  },
  methods: {
    beforeUpload(file) {
      const isValidType = /\.(jpg|jpeg|png|gif|bmp|webp|pdf)$/i.test(file.name)
      const isValidSize = file.size / 1024 / 1024 < this.maxSize

      if (!isValidType) {
        this.$message.error('只能上传图片或PDF文件!')
        return false
      }
      if (!isValidSize) {
        this.$message.error(`文件大小不能超过 ${this.maxSize}MB!`)
        return false
      }

      this.uploading = true
      this.uploadProgress = 0
      this.fileName = file.name
      return true
    },
    handleProgress(event) {
      this.uploadProgress = Math.round(event.percent)
    },
    handleSuccess(response) {
      this.uploading = false
      if (response.code === 0) {
        this.fileUrl = response.data.url
        this.$emit('input', this.fileUrl)
        this.$emit('success', response.data)
        this.$message.success('上传成功!')
      } else {
        this.$message.error(response.msg || '上传失败!')
      }
    },
    handleError(error) {
      this.uploading = false
      this.$message.error('上传失败!')
      console.error('Upload error:', error)
    },
    previewFile() {
      if (this.fileUrl) {
        this.previewVisible = true
      }
    },
    removeFile() {
      this.$confirm('确定要删除这个文件吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.fileUrl = ''
        this.fileName = ''
        this.$emit('input', '')
        this.$emit('remove')
        this.$message.success('删除成功!')
      })
    }
  }
}
</script>

<style scoped>
.file-upload {
  display: inline-block;
}

.upload-area {
  width: 200px;
  height: 150px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.upload-area:hover {
  border-color: #409EFF;
}

.upload-area.is-uploading {
  border-color: #409EFF;
  cursor: not-allowed;
}

.upload-placeholder {
  text-align: center;
  color: #999;
}

.upload-placeholder i {
  font-size: 28px;
  color: #c0c4cc;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
}

.upload-progress {
  text-align: center;
}

.progress-text {
  margin-top: 10px;
  font-size: 12px;
  color: #666;
}

.upload-success {
  width: 100%;
  height: 100%;
  position: relative;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.uploaded-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 10px;
}

.uploaded-file i {
  font-size: 40px;
  color: #409EFF;
  margin-bottom: 8px;
}

.file-name {
  font-size: 12px;
  color: #666;
  text-align: center;
  word-break: break-all;
}

.upload-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 5px;
  text-align: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.upload-success:hover .upload-actions {
  opacity: 1;
}

.preview-content {
  text-align: center;
}

.pdf-preview {
  padding: 40px;
}

.preview-link {
  color: #409EFF;
  text-decoration: none;
}

.preview-link:hover {
  text-decoration: underline;
}
</style>