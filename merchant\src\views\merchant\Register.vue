<template>
  <div class="merchant-register">
    <div class="register-container">
      <div class="register-header">
        <h2>商户注册</h2>
        <p>请填写真实有效的商户信息</p>
      </div>
      
      <el-form
        ref="registerForm"
        :model="registerForm"
        :rules="registerRules"
        label-width="120px"
        class="register-form"
      >
        <el-form-item label="商户名称" prop="mch_name">
          <el-input
            v-model="registerForm.mch_name"
            placeholder="请输入商户名称"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        
        <el-form-item label="联系人姓名" prop="contact_name">
          <el-input
            v-model="registerForm.contact_name"
            placeholder="请输入联系人姓名"
            maxlength="20"
            show-word-limit
          ></el-input>
        </el-form-item>
        
        <el-form-item label="联系电话" prop="contact_tel">
          <el-input
            v-model="registerForm.contact_tel"
            placeholder="请输入联系电话"
            maxlength="20"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="商户类型" prop="type">
          <el-select v-model="registerForm.type" placeholder="请选择商户类型" style="width: 100%">
            <el-option label="个人商户" :value="1"></el-option>
            <el-option label="企业商户" :value="2"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            :loading="submitting"
            @click="handleRegister"
            style="width: 100%"
          >
            {{ submitting ? '注册中...' : '立即注册' }}
          </el-button>
        </el-form-item>
        
        <div class="register-footer">
          <p>已有账户？<router-link to="/login">立即登录</router-link></p>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import { registerMerchant } from '@/api/merchant'

export default {
  name: 'MerchantRegister',
  data() {
    // 手机号验证
    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入联系电话'))
      } else if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error('请输入正确的手机号码'))
      } else {
        callback()
      }
    }

    return {
      submitting: false,
      registerForm: {
        mch_name: '',
        contact_name: '',
        contact_tel: '',
        type: 1
      },
      registerRules: {
        mch_name: [
          { required: true, message: '请输入商户名称', trigger: 'blur' },
          { min: 2, max: 50, message: '商户名称长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        contact_name: [
          { required: true, message: '请输入联系人姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '联系人姓名长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        contact_tel: [
          { required: true, validator: validatePhone, trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择商户类型', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    handleRegister() {
      this.$refs.registerForm.validate(async (valid) => {
        if (!valid) return

        this.submitting = true
        try {
          const response = await registerMerchant(this.registerForm)
          if (response.code === 0) {
            this.$message.success('注册成功！请等待审核')
            // 跳转到登录页面或KYC页面
            this.$router.push('/login')
          } else {
            this.$message.error(response.msg || '注册失败')
          }
        } catch (error) {
          console.error('Register error:', error)
          this.$message.error('注册失败，请稍后重试')
        } finally {
          this.submitting = false
        }
      })
    }
  }
}
</script>

<style scoped>
.merchant-register {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 500px;
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h2 {
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: 600;
}

.register-header p {
  color: #666;
  font-size: 14px;
}

.register-form {
  margin-top: 20px;
}

.register-footer {
  text-align: center;
  margin-top: 20px;
}

.register-footer p {
  color: #666;
  font-size: 14px;
}

.register-footer a {
  color: #409EFF;
  text-decoration: none;
}

.register-footer a:hover {
  text-decoration: underline;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-input {
  height: 44px;
}

.el-input__inner {
  height: 44px;
  line-height: 44px;
}

.el-button {
  height: 44px;
  font-size: 16px;
}
</style>