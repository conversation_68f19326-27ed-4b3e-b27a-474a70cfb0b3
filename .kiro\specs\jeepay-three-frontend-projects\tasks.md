# Jeepay 三端前端项目实施计划

## 任务列表

- [x] 1. 创建项目基础架构和通用组件






  - 初始化三个 Vue 项目（merchant、manager、cashier）
  - 配置 Vue CLI、Element UI、Vue Router、Vuex、Axios
  - 创建通用组件（Layout、Pagination、Upload）
  - 建立统一的 API 请求工具和错误处理机制
  - _需求: 7.1, 7.2, 7.3, 5.6_

- [-] 2. 实现认证和权限系统



  - [x] 2.1 开发登录认证模块


    - 创建登录页面组件，支持用户名密码登录
    - 实现 JWT token 管理和自动刷新机制
    - 编写登录 API 调用和状态管理
    - _需求: 5.1_


  - [x] 2.2 实现权限控制系统






    - 创建路由守卫，基于用户角色控制页面访问
    - 实现按钮级权限控制指令
    - 编写动态菜单生成逻辑
    - _需求: 5.2, 5.3_

- [x] 3. 开发商户端（Merchant）核心功能





  - [x] 3.1 实现商户注册和 KYC 模块


    - 创建商户注册表单，基于 t_merchant 表字段（mch_name, contact_name, contact_tel）
    - 实现 KYC 审核状态展示页面，显示 t_merchant.state 字段
    - 编写文件上传组件，支持身份证和营业执照上传
    - _需求: 1.1, 1.2_

  - [x] 3.2 开发交易管理模块


    - 创建订单列表页面，基于 t_order 表字段展示
    - 实现订单搜索功能（order_id, status, created_at）
    - 编写订单详情页面，显示完整订单信息
    - 添加订单导出功能
    - _需求: 1.3, 5.7_

  - [x] 3.3 实现结算管理模块


    - 创建结算记录列表，基于 t_settlement 表字段
    - 实现结算状态筛选和搜索功能
    - 编写结算详情页面和申请结算功能
    - _需求: 1.4_

  - [x] 3.4 开发商户钱包和 USDT 充值



    - 创建钱包余额展示页面
    - 实现 USDT 充值功能和充值记录查询
    - 编写资金流水记录页面
    - _需求: 1.6, 1.7_


  - [x] 3.5 实现通道配置模块

    - 创建支付通道配置页面
    - 实现通道参数设置和测试功能
    - 编写通道状态监控页面
    - _需求: 1.5_

- [ ] 4. 开发管理端（Manager）核心功能





  - [x] 4.1 实现商户管理模块


    - 创建商户列表页面，基于 t_merchant 表字段展示
    - 实现商户搜索、筛选、状态管理功能
    - 编写商户详情页面和编辑功能
    - 添加商户审核和状态修改功能
    - _需求: 2.2_




  - [x] 4.2 开发订单管理模块

    - 创建订单管理列表，基于 t_order 表字段
    - 实现多条件搜索和高级筛选功能
    - 编写订单详情页面，支持退款和状态修改


    - 添加批量操作和数据导出功能
    - _需求: 2.3, 5.7_

  - [x] 4.3 实现结算管理模块


    - 创建结算审核列表，基于 t_settlement 表字段
    - 实现结算审核流程和批量处理功能
    - 编写结算统计和报表功能
    - _需求: 2.5_




  - [x] 4.4 开发交易风控模块

    - 创建风险监控仪表盘
    - 实现风控规则配置和管理
    - 编写异常交易预警和处理功能


    - _需求: 2.4_

  - [x] 4.5 实现通道管理模块

    - 创建支付通道管理页面
    - 实现通道配置、监控和统计功能
    - 编写通道费率和限额管理
    - _需求: 2.6_

  - [x] 4.6 开发 CardDealer 卡商管理模块


    - 创建卡商管理列表，基于 t_carddealer 表字段（dealer_id, dealer_name, status, balance）
    - 实现卡商新增、编辑、状态管理功能
    - 编写卡商余额管理和充值功能
    - _需求: 2.8_

  - [x] 4.7 实现卡库存管理模块

    - 创建卡库存列表，基于 t_card_inventory 表字段（card_id, dealer_id, card_number, status）
    - 实现卡片批量导入和导出功能
    - 编写卡片状态管理和库存统计
    - 添加卡交易记录查询功能
    - _需求: 2.8_

  - [x] 4.8 开发统计报表模块


    - 创建交易统计仪表盘
    - 实现商户统计和收入统计报表
    - 编写数据可视化图表组件
    - 添加报表导出和定时推送功能
    - _需求: 2.7_

- [x] 5. 开发收银台（Cashier）支付功能




  - [x] 5.1 实现订单信息展示


    - 创建订单信息展示组件，基于 t_order 表字段
    - 实现订单金额、商品信息、有效期展示
    - 编写订单验证和安全检查逻辑
    - _需求: 3.1_

  - [x] 5.2 开发多种支付方式


    - 创建外卡支付表单组件
    - 实现 USDT 支付钱包地址生成和二维码展示
    - 编写本地支付方式选择和表单
    - _需求: 3.2_



  - [ ] 5.3 实现支付处理和轮询
    - 编写支付提交和处理逻辑
    - 实现支付状态轮询机制
    - 创建支付进度展示组件


    - _需求: 3.3_

  - [ ] 5.4 开发支付结果页面
    - 创建支付成功、失败、处理中状态页面
    - 实现支付结果展示和订单信息确认
    - 编写重新支付和支付方式切换功能
    - _需求: 3.4, 3.5_

- [x] 6. 实现国际化和通用功能





  - [x] 6.1 配置国际化系统


    - 安装和配置 vue-i18n 插件
    - 创建中文和英文语言包文件
    - 实现语言切换功能和本地存储
    - _需求: 5.4_


  - [x] 6.2 实现全局错误处理

    - 创建全局错误拦截器和处理机制
    - 实现友好错误提示和用户引导
    - 编写错误日志记录和上报功能
    - _需求: 5.5_



  - [ ] 6.3 开发 Excel 导出功能
    - 安装和配置 xlsx 导出库
    - 实现列表数据导出功能
    - 编写自定义导出格式和字段选择
    - _需求: 5.6_

- [x] 7. 优化和完善项目




  - [x] 7.1 实现响应式布局


    - 优化移动端适配和响应式设计
    - 实现左侧菜单折叠和移动端抽屉
    - 编写面包屑导航和页面标题管理
    - _需求: 6.5_



  - [x] 7.2 性能优化和代码分割

    - 实现路由懒加载和代码分割
    - 优化图片懒加载和资源压缩
    - 编写缓存策略和性能监控


    - _需求: 6.1, 6.2, 6.3, 6.4, 6.5, 6.6_

  - [x] 7.3 完善表单验证和用户体验

    - 实现统一的表单验证规则
    - 优化加载状态和用户反馈
    - 编写操作确认和成功提示
    - _需求: 6.4, 6.6_

- [ ] 8. 项目配置和文档









  - [ ] 8.1 配置构建和部署


    - 配置生产环境构建脚本
    - 实现环境变量管理和 API 代理
    - 编写 Docker 配置和部署脚本
    - _需求: 7.6, 7.7_

  - [ ] 8.2 编写项目文档
    - 创建详细的 README.md 文件，包含安装、运行、打包说明
    - 编写 API 接口文档和组件使用说明
    - 添加开发规范和代码注释
    - _需求: 7.6_

  - [ ] 8.3 代码质量控制
    - 配置 ESLint 和 Prettier 代码规范
    - 实现代码提交前检查和格式化
    - 编写单元测试和集成测试用例
    - _需求: 7.1_