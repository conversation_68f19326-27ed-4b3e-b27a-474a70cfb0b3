import store from '@/store'

/**
 * 检查用户是否有指定权限
 * @param {Array|String} permission 权限码或权限码数组
 * @returns {Boolean}
 */
export function hasPermission(permission) {
  const userPermissions = store.getters['user/permissions']
  
  if (!userPermissions || userPermissions.length === 0) {
    return false
  }
  
  if (typeof permission === 'string') {
    return userPermissions.includes(permission)
  }
  
  if (Array.isArray(permission)) {
    return permission.some(p => userPermissions.includes(p))
  }
  
  return false
}

/**
 * 检查用户是否有指定角色
 * @param {Array|String} role 角色或角色数组
 * @returns {Boolean}
 */
export function hasRole(role) {
  const userInfo = store.getters['user/userInfo']
  const userRoles = userInfo.roles || []
  
  if (!userRoles || userRoles.length === 0) {
    return false
  }
  
  if (typeof role === 'string') {
    return userRoles.includes(role)
  }
  
  if (Array.isArray(role)) {
    return role.some(r => userRoles.includes(r))
  }
  
  return false
}

/**
 * 检查路由权限
 * @param {Object} route 路由对象
 * @param {Object} userInfo 用户信息
 * @returns {Boolean}
 */
export function checkRoutePermission(route, userInfo) {
  // 如果路由没有设置权限要求，默认允许访问
  if (!route.meta || (!route.meta.permission && !route.meta.roles)) {
    return true
  }
  
  // 检查权限码
  if (route.meta.permission) {
    if (!hasPermission(route.meta.permission)) {
      return false
    }
  }
  
  // 检查角色
  if (route.meta.roles) {
    if (!hasRole(route.meta.roles)) {
      return false
    }
  }
  
  return true
}

/**
 * 过滤有权限的路由
 * @param {Array} routes 路由数组
 * @param {Object} userInfo 用户信息
 * @returns {Array}
 */
export function filterRoutes(routes, userInfo) {
  const filteredRoutes = []
  
  routes.forEach(route => {
    const tmp = { ...route }
    
    if (checkRoutePermission(tmp, userInfo)) {
      if (tmp.children) {
        tmp.children = filterRoutes(tmp.children, userInfo)
      }
      filteredRoutes.push(tmp)
    }
  })
  
  return filteredRoutes
}