{"name": "jeepay-merchant", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:prod": "vue-cli-service build --mode production", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "vue-cli-service lint", "lint:fix": "vue-cli-service lint --fix"}, "dependencies": {"axios": "^0.27.2", "element-ui": "^2.15.9", "file-saver": "^2.0.5", "vue": "^2.6.14", "vue-i18n": "^8.27.2", "vue-router": "^3.5.4", "vuex": "^3.6.2", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-standard": "^6.1.0", "chalk": "^4.1.2", "connect": "^3.7.0", "eslint": "^7.32.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.2.0", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-vue": "^8.7.1", "runjs": "^4.4.2", "script-ext-html-webpack-plugin": "^2.1.5", "serve-static": "^1.15.0", "vue-template-compiler": "^2.6.14"}}