/**
 * 统一的表单验证规则
 */

// 常用正则表达式
export const REGEX = {
  // 手机号
  mobile: /^1[3-9]\d{9}$/,
  // 邮箱
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  // 身份证号
  idCard: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  // 银行卡号
  bankCard: /^[1-9]\d{12,19}$/,
  // 密码（8-20位，包含字母和数字）
  password: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,20}$/,
  // 用户名（4-20位字母数字下划线）
  username: /^[a-zA-Z0-9_]{4,20}$/,
  // 中文姓名
  chineseName: /^[\u4e00-\u9fa5]{2,10}$/,
  // 英文姓名
  englishName: /^[a-zA-Z\s]{2,50}$/,
  // URL
  url: /^https?:\/\/(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/i,
  // IP地址
  ip: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
  // 数字
  number: /^\d+$/,
  // 小数
  decimal: /^\d+(\.\d+)?$/,
  // 正整数
  positiveInteger: /^[1-9]\d*$/,
  // 非负整数
  nonNegativeInteger: /^(0|[1-9]\d*)$/
}

// 基础验证规则
export const baseRules = {
  // 必填项
  required: (message = '此项为必填项') => ({
    required: true,
    message,
    trigger: 'blur'
  }),

  // 长度验证
  length: (min, max, message) => ({
    min,
    max,
    message: message || `长度在 ${min} 到 ${max} 个字符`,
    trigger: 'blur'
  }),

  // 最小长度
  minLength: (min, message) => ({
    min,
    message: message || `最少输入 ${min} 个字符`,
    trigger: 'blur'
  }),

  // 最大长度
  maxLength: (max, message) => ({
    max,
    message: message || `最多输入 ${max} 个字符`,
    trigger: 'blur'
  }),

  // 正则验证
  pattern: (pattern, message) => ({
    pattern,
    message,
    trigger: 'blur'
  }),

  // 自定义验证
  validator: (validator, message, trigger = 'blur') => ({
    validator,
    message,
    trigger
  })
}

// 常用字段验证规则
export const fieldRules = {
  // 用户名
  username: [
    baseRules.required('请输入用户名'),
    baseRules.pattern(REGEX.username, '用户名只能包含字母、数字和下划线，长度4-20位')
  ],

  // 密码
  password: [
    baseRules.required('请输入密码'),
    baseRules.pattern(REGEX.password, '密码必须包含字母和数字，长度8-20位')
  ],

  // 确认密码
  confirmPassword: (passwordField = 'password') => [
    baseRules.required('请确认密码'),
    baseRules.validator((rule, value, callback) => {
      if (value !== this.form[passwordField]) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    })
  ],

  // 手机号
  mobile: [
    baseRules.required('请输入手机号'),
    baseRules.pattern(REGEX.mobile, '请输入正确的手机号')
  ],

  // 邮箱
  email: [
    baseRules.required('请输入邮箱'),
    baseRules.pattern(REGEX.email, '请输入正确的邮箱格式')
  ],

  // 身份证号
  idCard: [
    baseRules.required('请输入身份证号'),
    baseRules.pattern(REGEX.idCard, '请输入正确的身份证号')
  ],

  // 银行卡号
  bankCard: [
    baseRules.required('请输入银行卡号'),
    baseRules.pattern(REGEX.bankCard, '请输入正确的银行卡号')
  ],

  // 中文姓名
  chineseName: [
    baseRules.required('请输入姓名'),
    baseRules.pattern(REGEX.chineseName, '请输入正确的中文姓名')
  ],

  // 英文姓名
  englishName: [
    baseRules.required('请输入姓名'),
    baseRules.pattern(REGEX.englishName, '请输入正确的英文姓名')
  ],

  // URL
  url: [
    baseRules.required('请输入URL'),
    baseRules.pattern(REGEX.url, '请输入正确的URL格式')
  ],

  // 金额
  amount: [
    baseRules.required('请输入金额'),
    baseRules.pattern(REGEX.decimal, '请输入正确的金额格式'),
    baseRules.validator((rule, value, callback) => {
      if (parseFloat(value) <= 0) {
        callback(new Error('金额必须大于0'))
      } else {
        callback()
      }
    })
  ],

  // 商户号
  merchantNo: [
    baseRules.required('请输入商户号'),
    baseRules.length(8, 20, '商户号长度为8-20位'),
    baseRules.pattern(/^[A-Z0-9]+$/, '商户号只能包含大写字母和数字')
  ],

  // 订单号
  orderNo: [
    baseRules.required('请输入订单号'),
    baseRules.length(10, 32, '订单号长度为10-32位')
  ],

  // 联系电话（座机或手机）
  phone: [
    baseRules.required('请输入联系电话'),
    baseRules.validator((rule, value, callback) => {
      const mobilePattern = REGEX.mobile
      const telPattern = /^0\d{2,3}-?\d{7,8}$/
      if (!mobilePattern.test(value) && !telPattern.test(value)) {
        callback(new Error('请输入正确的手机号或座机号'))
      } else {
        callback()
      }
    })
  ],

  // 验证码
  verifyCode: [
    baseRules.required('请输入验证码'),
    baseRules.length(4, 6, '验证码长度为4-6位'),
    baseRules.pattern(REGEX.number, '验证码只能是数字')
  ]
}

// 业务相关验证规则
export const businessRules = {
  // 商户相关
  merchant: {
    name: [
      baseRules.required('请输入商户名称'),
      baseRules.length(2, 50, '商户名称长度为2-50个字符')
    ],
    contactName: fieldRules.chineseName,
    contactTel: fieldRules.mobile,
    businessLicense: [
      baseRules.required('请输入营业执照号'),
      baseRules.length(15, 18, '营业执照号长度为15-18位')
    ]
  },

  // 订单相关
  order: {
    amount: fieldRules.amount,
    currency: [
      baseRules.required('请选择货币类型')
    ],
    description: [
      baseRules.maxLength(200, '订单描述最多200个字符')
    ]
  },

  // 结算相关
  settlement: {
    amount: fieldRules.amount,
    bankName: [
      baseRules.required('请输入银行名称'),
      baseRules.length(2, 50, '银行名称长度为2-50个字符')
    ],
    bankAccount: fieldRules.bankCard,
    accountName: fieldRules.chineseName
  },

  // 通道相关
  channel: {
    name: [
      baseRules.required('请输入通道名称'),
      baseRules.length(2, 30, '通道名称长度为2-30个字符')
    ],
    code: [
      baseRules.required('请输入通道编码'),
      baseRules.pattern(/^[A-Z_]+$/, '通道编码只能包含大写字母和下划线')
    ],
    rate: [
      baseRules.required('请输入费率'),
      baseRules.validator((rule, value, callback) => {
        const rate = parseFloat(value)
        if (isNaN(rate) || rate < 0 || rate > 100) {
          callback(new Error('费率必须在0-100之间'))
        } else {
          callback()
        }
      })
    ]
  }
}

// 动态验证规则生成器
export const createRules = (config) => {
  const rules = {}
  
  Object.keys(config).forEach(field => {
    const fieldConfig = config[field]
    rules[field] = []
    
    // 必填验证
    if (fieldConfig.required) {
      rules[field].push(baseRules.required(fieldConfig.requiredMessage))
    }
    
    // 长度验证
    if (fieldConfig.minLength || fieldConfig.maxLength) {
      rules[field].push(baseRules.length(
        fieldConfig.minLength || 0,
        fieldConfig.maxLength || 999,
        fieldConfig.lengthMessage
      ))
    }
    
    // 正则验证
    if (fieldConfig.pattern) {
      rules[field].push(baseRules.pattern(
        fieldConfig.pattern,
        fieldConfig.patternMessage
      ))
    }
    
    // 自定义验证
    if (fieldConfig.validator) {
      rules[field].push(baseRules.validator(
        fieldConfig.validator,
        fieldConfig.validatorMessage
      ))
    }
  })
  
  return rules
}

// 表单验证混入
export const formValidationMixin = {
  data() {
    return {
      formLoading: false,
      formErrors: {}
    }
  },
  methods: {
    // 验证整个表单
    async validateForm(formRef = 'form') {
      try {
        await this.$refs[formRef].validate()
        return true
      } catch (error) {
        this.$message.error('请检查表单填写是否正确')
        return false
      }
    },

    // 验证单个字段
    async validateField(field, formRef = 'form') {
      try {
        await this.$refs[formRef].validateField(field)
        return true
      } catch (error) {
        return false
      }
    },

    // 清除验证
    clearValidation(formRef = 'form') {
      this.$refs[formRef].clearValidate()
      this.formErrors = {}
    },

    // 重置表单
    resetForm(formRef = 'form') {
      this.$refs[formRef].resetFields()
      this.formErrors = {}
    },

    // 设置字段错误
    setFieldError(field, message) {
      this.$set(this.formErrors, field, message)
    },

    // 清除字段错误
    clearFieldError(field) {
      this.$delete(this.formErrors, field)
    }
  }
}

export default {
  REGEX,
  baseRules,
  fieldRules,
  businessRules,
  createRules,
  formValidationMixin
}