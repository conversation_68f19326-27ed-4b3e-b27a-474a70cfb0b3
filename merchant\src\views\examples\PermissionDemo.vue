<template>
  <div class="permission-demo">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>权限控制演示</span>
      </div>
      
      <div class="demo-section">
        <h3>指令权限控制</h3>
        <p>以下按钮根据用户权限动态显示/隐藏：</p>
        
        <el-button 
          v-permission="['merchant:create']" 
          type="primary"
        >
          新增商户（需要merchant:create权限）
        </el-button>
        
        <el-button 
          v-permission="['merchant:edit']" 
          type="warning"
        >
          编辑商户（需要merchant:edit权限）
        </el-button>
        
        <el-button 
          v-permission="['merchant:delete']" 
          type="danger"
        >
          删除商户（需要merchant:delete权限）
        </el-button>
        
        <el-button 
          v-role="['admin']" 
          type="success"
        >
          管理员功能（需要admin角色）
        </el-button>
      </div>
      
      <div class="demo-section">
        <h3>方法权限控制</h3>
        <p>以下内容根据权限动态渲染：</p>
        
        <div v-if="$hasPermission(['order:view'])">
          <el-alert
            title="您有订单查看权限"
            type="success"
            :closable="false">
          </el-alert>
        </div>
        
        <div v-if="$hasRole(['admin', 'manager'])">
          <el-alert
            title="您是管理员或经理"
            type="info"
            :closable="false">
          </el-alert>
        </div>
        
        <div v-if="!$hasPermission(['settlement:audit'])">
          <el-alert
            title="您没有结算审核权限"
            type="warning"
            :closable="false">
          </el-alert>
        </div>
      </div>
      
      <div class="demo-section">
        <h3>当前用户信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">
            {{ userInfo.id || '未登录' }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            {{ userInfo.username || '未登录' }}
          </el-descriptions-item>
          <el-descriptions-item label="角色">
            {{ (userInfo.roles || []).join(', ') || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="权限数量">
            {{ (permissions || []).length }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 16px;">
          <el-tag 
            v-for="permission in permissions" 
            :key="permission"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ permission }}
          </el-tag>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import permissionMixin from '@/mixins/permission'

export default {
  name: 'PermissionDemo',
  mixins: [permissionMixin],
  computed: {
    ...mapGetters('user', ['userInfo', 'permissions'])
  }
}
</script>

<style scoped>
.permission-demo {
  padding: 20px;
}

.demo-section {
  margin-bottom: 32px;
}

.demo-section h3 {
  margin-bottom: 16px;
  color: #303133;
}

.demo-section p {
  margin-bottom: 16px;
  color: #606266;
}

.el-button {
  margin-right: 12px;
  margin-bottom: 12px;
}

.el-alert {
  margin-bottom: 12px;
}
</style>