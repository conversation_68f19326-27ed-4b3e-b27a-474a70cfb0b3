import axios from 'axios'
import { Message, MessageBox } from 'element-ui'
import store from '@/store'
import router from '@/router'
import errorHandler from './errorHandler'

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API || '/api',
  timeout: 15000
})

// 是否正在刷新token
let isRefreshing = false
// 重试队列
let requests = []

// 请求拦截器
service.interceptors.request.use(
  async config => {
    // 检查token是否即将过期
    await store.dispatch('user/checkTokenExpire').catch(() => {})
    
    // 添加token
    const token = store.getters['user/token'] || localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    
    // 添加请求头
    config.headers['Content-Type'] = 'application/json'
    
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    
    // 如果返回的状态码不是200，则认为是错误
    if (res.code !== 0) {
      Message({
        message: res.msg || '请求失败',
        type: 'error',
        duration: 5 * 1000
      })
      
      // 401: 未授权，尝试刷新token
      if (res.code === 401) {
        return handleTokenExpired(response.config)
      }
      
      return Promise.reject(new Error(res.msg || '请求失败'))
    } else {
      return res
    }
  },
  error => {
    console.error('响应错误:', error)
    
    // 使用全局错误处理器
    errorHandler.handleHttpError(error)
    
    // 特殊处理401错误
    if (error.response && error.response.status === 401) {
      return handleTokenExpired(error.config)
    }
    
    return Promise.reject(error)
  }
)

// 处理token过期
function handleTokenExpired(config) {
  const refreshToken = store.getters['user/refreshToken']
  
  if (!refreshToken) {
    // 没有refresh token，直接跳转登录
    redirectToLogin()
    return Promise.reject(new Error('No refresh token'))
  }
  
  if (!isRefreshing) {
    isRefreshing = true
    
    return store.dispatch('user/refreshToken').then(() => {
      // 刷新成功，重新发送所有请求
      requests.forEach(cb => cb())
      requests = []
      isRefreshing = false
      
      // 重新发送当前请求
      const token = store.getters['user/token']
      config.headers['Authorization'] = `Bearer ${token}`
      return service(config)
    }).catch(() => {
      // 刷新失败，跳转登录
      requests = []
      isRefreshing = false
      redirectToLogin()
      return Promise.reject(new Error('Token refresh failed'))
    })
  } else {
    // 正在刷新token，将请求加入队列
    return new Promise(resolve => {
      requests.push(() => {
        const token = store.getters['user/token']
        config.headers['Authorization'] = `Bearer ${token}`
        resolve(service(config))
      })
    })
  }
}

// 跳转到登录页
function redirectToLogin() {
  MessageBox.confirm('登录状态已过期，请重新登录', '系统提示', {
    confirmButtonText: '重新登录',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    store.dispatch('user/logout')
    router.push('/login')
  }).catch(() => {
    store.dispatch('user/logout')
    router.push('/login')
  })
}

export default service