{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;AAAA,gDAAwB;AACxB,mDAA+C;AAG/C,IAAI,KAAK,GAAG,QAAQ,CAAC;AAErB,SAAS,eAAe,CAAC,QAAQ;IAC/B,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;QACvB,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;KACnF;AACH,CAAC;AAED,SAAS,aAAa,CAAC,QAAQ,EAAE,QAAQ;IACvC,OAAO,cAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACtF,CAAC;AAED,SAAS,iBAAiB,CAAC,MAAM;IAC/B,OAAO,CAAC,gBAAgB,EAAE,EAAE;QAG1B,IAAI,gBAAgB,CAAC,KAAK,EAAE;YAC1B,MAAM,WAAW,GAAG,gBAAgB,CAAC,aAAa,CAAC;YACnD,MAAM,QAAQ,GAAG,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACvD,OAAO;gBACL,MAAM;gBACN,KAAK,EAAE,QAAQ;aAChB,CAAC;SACH;QAED,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACxB,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,OAAO,CAAC,OAAO,EAAE,GAAG;IAE3B,IAAI,OAAO,CAAC,KAAK,YAAY,GAAG,EAAE;QAChC,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC/B;SAAM,IAAI,OAAO,CAAC,KAAK,EAAE;QACxB,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAC1B;SAAM,IAAI,OAAO,CAAC,IAAI,YAAY,GAAG,EAAE;QAEtC,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC9B;SAAM;QACL,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAC1B;AACH,CAAC;AAED,SAAS,OAAO,CAAC,gBAAgB,EAAE,GAAG,EAAE,YAAY;IAClD,MAAM,KAAK,GAAG,YAAY,CAAC,gBAAgB,CAAC,CAAC;IAG7C,IAAI,gBAAgB,CAAC,KAAK,YAAY,GAAG,EAAE;QACzC,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KACxC;SAAM,IAAI,gBAAgB,CAAC,KAAK,EAAE;QACjC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;KACpC;SAAM,IAAI,gBAAgB,CAAC,IAAI,YAAY,GAAG,EAAE;QAE/C,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KACvC;SAAM;QACL,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;KACpC;AACH,CAAC;AAED,SAAS,cAAc,CAAC,UAAU;IAChC,IAAI,UAAU,CAAC,YAAY,EAAE;QAE3B,OAAO,UAAU,CAAC,YAAY,CAAC;KAChC;SAAM,IAAI,UAAU,CAAC,YAAY,EAAE;QAElC,OAAO,UAAU,CAAC,YAAY,CAAC;KAChC;SAAM;QAEL,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;KACjD;AACH,CAAC;AAED,SAAS,cAAc,CAAC,UAAU;IAChC,IAAI,UAAU,CAAC,gBAAgB,EAAE;QAE/B,OAAO,UAAU,CAAC,gBAAgB,CAAC;KACpC;SAAM,IAAI,UAAU,CAAC,gBAAgB,EAAE;QAEtC,OAAO,UAAU,CAAC,gBAAgB,CAAC;KACpC;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;KACpD;AACH,CAAC;AAED,SAAS,iBAAiB,CAAC,UAAU;IACnC,IAAI,UAAU,CAAC,eAAe,EAAE;QAC9B,OAAO,UAAU,CAAC,eAAe,CAAC;KACnC;SAAM,IAAI,UAAU,CAAC,eAAe,EAAE;QACrC,OAAO,UAAU,CAAC,eAAe,CAAC;KACnC;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;KAC1E;AACH,CAAC;AAED,MAAM,oBAAoB;IAKxB,YAAmB,OAAgC;QAH3C,cAAS,GAAoB,IAAI,CAAC;QAClC,aAAQ,GAAQ,IAAI,CAAC;QAG3B,IAAI,CAAC,cAAc,GAAG,OAAO,IAAI,IAAI,CAAC;IACxC,CAAC;IAEM,WAAW,CAAC,QAAgB,EAAE,QAAgB;QACnD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;SACpD;QAED,eAAe,CAAC,IAAI,CAAC,CAAC;QAEtB,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACxB,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QAE5B,MAAM,KAAK,GAAG,IAAI,4BAAY,CAAC;YAC7B,GAAG,EAAE,OAAO;YACZ,KAAK,EAAE,CAAC;YACR,GAAG,EAAE,IAAI;YACT,GAAG,EAAE,IAAI;YACT,IAAI,EAAE,CAAC;YACP,OAAO,EAAE,IAAI;YACb,GAAG,EAAE,KAAK,EAAE;YACZ,IAAI,EAAE,KAAK;YACX,IAAI,EAAE,GAAG;YACT,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;YAC9B,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,IAAI;YACX,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QACH,MAAM,UAAU,GAAG,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAE3D,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS;YAEvB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,uBAAuB,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAKlF,IAAI,oBAAoB,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;QAE1E,OAAO,oBAAoB,IAAI,oBAAoB,CAAC,GAAG,EAAE;YACvD,oBAAoB,GAAG,oBAAoB,CAAC,GAAG,CAAC;SACjD;QAED,IAAI,oBAAoB,GAAQ,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;QAC/D,OAAO,oBAAoB,IAAI,oBAAoB,CAAC,gBAAgB,EAAE;YACpE,oBAAoB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC;SAC9D;QAED,oBAAoB,CAAC,iBAAiB,CAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACpE,IACE,oBAAoB;YACpB,CAAC,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,EACpG;YACA,MAAM,YAAY,GAChB,oBAAoB,CAAC,OAAO,CAAC,YAAY,YAAY,GAAG;gBACtD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;gBAChE,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC;YAChD,KAAK,IAAI,WAAW,IAAI,YAAY,EAAE;gBACpC,IAAI,SAAS,IAAI,WAAW,EAAE;oBAC5B,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;iBACnC;gBACD,IAAI,WAAW,CAAC,IAAI,KAAK,UAAU,EAAE;oBACnC,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK;wBAEnB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,mBAAmB,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;oBAC1E,OAAO,WAAW,CAAC,gBAAgB,CAAC,sBAAsB,CAAC;oBAC3D,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;iBACxC;aACF;SACF;IACH,CAAC;IAEM,KAAK,CAAC,QAAkB;QAC7B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE1B,MAAM,oBAAoB,GAAG,GAAG,EAAE;YAChC,IAAI,oBAAoB,GAAQ,QAAQ,CAAC,eAAe,CAAC;YACzD,OAAO,oBAAoB,IAAI,oBAAoB,CAAC,gBAAgB,EAAE;gBACpE,oBAAoB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC;aAC9D;YAED,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,EAAE;gBAC3C,MAAM,aAAa,GAAG,oBAAoB,CAAC,KAAK,CAAC;gBAEjD,oBAAoB,CAAC,KAAK,GAAG,GAAG,EAAE;oBAChC,aAAa,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;oBAC9C,IAAI,oBAAoB,CAAC,aAAa,EAAE;wBACtC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;4BAC/D,MAAM,IAAI,GAAG,oBAAoB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;4BACtD,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAC1E,CAAC,CAAC,CAAC;qBACJ;gBACH,CAAC,CAAC;gBAEF,oBAAoB,CAAC,iBAAiB,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;oBACjE,MAAM,WAAW,GAAG,cAAc,CAAC,oBAAoB,CAAC,CAAC;oBACzD,MAAM,WAAW,GAAG,cAAc,CAAC,oBAAoB,CAAC,CAAC;oBACzD,MAAM,cAAc,GAAG,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;oBAC/D,oBAAoB,CAAC,aAAa,GAAG,oBAAoB,CAAC,aAAa,IAAI,EAAE,CAAC;oBAC9E,oBAAoB,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;oBAChF,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;oBACrD,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;oBACrC,IAAI,KAAK,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;oBAChC,MAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrC,OAAO,KAAK,GAAG,QAAQ,EAAE;wBACvB,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,cAAI,CAAC,GAAG,CAAC,IAAI,cAAI,CAAC,GAAG,CAAC;wBAChE,IAAI;4BACF,oBAAoB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;yBACvC;wBAAC,OAAO,CAAC,EAAE;4BACV,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;4BACxB,MAAM,QAAQ,GAAG,IAAI,4BAAY,CAAC;gCAChC,GAAG,EAAE,OAAO;gCACZ,KAAK,EAAE,CAAC;gCACR,GAAG,EAAE,IAAI;gCACT,GAAG,EAAE,IAAI;gCACT,IAAI,EAAE,CAAC;gCACP,OAAO,EAAE,IAAI;gCACb,GAAG,EAAE,KAAK,EAAE;gCACZ,IAAI,EAAE,KAAK;gCACX,IAAI,EAAE,KAAK,CAAC,IAAI;gCAChB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;gCACrC,KAAK,EAAE,IAAI;gCACX,KAAK,EAAE,IAAI;gCACX,KAAK,EAAE,IAAI;gCACX,SAAS,EAAE,IAAI;6BAChB,CAAC,CAAC;4BAEH,OAAO,CAAC,cAAc,EAAE,GAAG,EAAE,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;4BACpD,OAAO,CAAC,WAAW,EAAE,GAAG,EAAE,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;yBACxD;wBACD,IAAI,OAAO,GAAG,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,EAAE,GAAG,CAAC,CAAC;wBAEpE,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC;wBACvC,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;wBACjC,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;4BACjC,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;4BAChD,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,EAAE,GAAG,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;yBACjF;6BAAM;4BACL,MAAM;yBACP;wBACD,KAAK,EAAE,CAAC;qBACT;gBACH,CAAC,CAAC;aACH;QACH,CAAC,CAAC;QACF,MAAM,kBAAkB,GAAG,GAAG,EAAE;YAC9B,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;oBACtE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;iBACtC;gBACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;aAC5B;QACH,CAAC,CAAC;QAEF,MAAM,YAAY,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC;YAC5C,MAAM,YAAY,GAAI,QAAgB,CAAC,eAAe,CAAC,aAAa,CAAC;YACrE,MAAM,GAAG,GAAG,QAAQ,CAAC,cAAqB,CAAC;YAC3C,IAAI,YAAY,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,GAAG,KAAK,UAAU,EAAE;gBACxD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBACzC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACjD,CAAC,CAAC,CAAC;aACJ;YACD,QAAQ,EAAE,CAAC;QACb,CAAC,CAAC;QAEF,IAAI,QAAQ,CAAC,KAAK,EAAE;YAClB,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,sBAAsB,EAAE,oBAAoB,CAAC,CAAC;YAClF,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,sBAAsB,EAAE,kBAAkB,CAAC,CAAC;YAC9E,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC;SACxE;aAAM;YACJ,QAAgB,CAAC,MAAM,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;YACnE,QAAgB,CAAC,MAAM,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;YAC/D,QAAgB,CAAC,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;SACrD;IACH,CAAC;CACF;AAED,iBAAS,oBAAoB,CAAC"}