<template>
  <div class="order-list">
    <div class="page-header">
      <h2>订单管理</h2>
      <p>查看和管理所有交易订单</p>
    </div>
    
    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form
        ref="searchForm"
        :model="searchForm"
        :inline="true"
        label-width="80px"
        class="search-form"
      >
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.order_id"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="订单状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 350px"
          ></el-date-picker>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
          <el-button type="success" @click="handleExport" :loading="exporting">
            <i class="el-icon-download"></i> 导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 订单统计 -->
    <div class="stats-row">
      <el-card class="stats-item">
        <div class="stats-content">
          <div class="stats-value">{{ stats.totalCount }}</div>
          <div class="stats-label">总订单数</div>
        </div>
      </el-card>
      <el-card class="stats-item">
        <div class="stats-content">
          <div class="stats-value">¥{{ formatMoney(stats.totalAmount) }}</div>
          <div class="stats-label">总金额</div>
        </div>
      </el-card>
      <el-card class="stats-item">
        <div class="stats-content">
          <div class="stats-value">{{ stats.successCount }}</div>
          <div class="stats-label">成功订单</div>
        </div>
      </el-card>
      <el-card class="stats-item">
        <div class="stats-content">
          <div class="stats-value">{{ (stats.successRate * 100).toFixed(1) }}%</div>
          <div class="stats-label">成功率</div>
        </div>
      </el-card>
    </div>
    
    <!-- 订单列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="orderList"
        stripe
        border
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="order_id" label="订单号" width="180" fixed="left">
          <template slot-scope="scope">
            <el-link type="primary" @click="viewDetail(scope.row)">
              {{ scope.row.order_id }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="mch_no" label="商户号" width="120"></el-table-column>
        
        <el-table-column prop="amount" label="订单金额" width="120" sortable="custom">
          <template slot-scope="scope">
            <span class="amount-text">¥{{ formatMoney(scope.row.amount) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="currency" label="币种" width="80"></el-table-column>
        
        <el-table-column prop="status" label="订单状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="pay_method" label="支付方式" width="120">
          <template slot-scope="scope">
            {{ getPayMethodText(scope.row.pay_method) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="160" sortable="custom">
          <template slot-scope="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="updated_at" label="更新时间" width="160" sortable="custom">
          <template slot-scope="scope">
            {{ formatTime(scope.row.updated_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="viewDetail(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        ></el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getOrderList, getOrderStats, exportOrders, ORDER_STATUS, getOrderStatusOptions } from '@/api/order'

export default {
  name: 'OrderList',
  data() {
    return {
      loading: false,
      exporting: false,
      orderList: [],
      searchForm: {
        order_id: '',
        status: '',
        mch_no: ''
      },
      dateRange: [],
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      sortField: '',
      sortOrder: '',
      stats: {
        totalCount: 0,
        totalAmount: 0,
        successCount: 0,
        successRate: 0
      }
    }
  },
  computed: {
    statusOptions() {
      return getOrderStatusOptions()
    },
    searchParams() {
      const params = {
        page: this.pagination.page,
        size: this.pagination.size,
        ...this.searchForm
      }
      
      if (this.dateRange && this.dateRange.length === 2) {
        params.created_at_start = this.dateRange[0]
        params.created_at_end = this.dateRange[1]
      }
      
      if (this.sortField) {
        params.sort_field = this.sortField
        params.sort_order = this.sortOrder
      }
      
      return params
    }
  },
  created() {
    this.fetchOrderList()
    this.fetchStats()
  },
  methods: {
    async fetchOrderList() {
      this.loading = true
      try {
        const response = await getOrderList(this.searchParams)
        if (response.code === 0) {
          this.orderList = response.data.list || []
          this.pagination.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || '获取订单列表失败')
        }
      } catch (error) {
        console.error('Fetch order list error:', error)
        this.$message.error('获取订单列表失败')
      } finally {
        this.loading = false
      }
    },
    async fetchStats() {
      try {
        const params = {}
        if (this.dateRange && this.dateRange.length === 2) {
          params.date_start = this.dateRange[0].split(' ')[0]
          params.date_end = this.dateRange[1].split(' ')[0]
        }
        
        const response = await getOrderStats(params)
        if (response.code === 0) {
          this.stats = response.data
        }
      } catch (error) {
        console.error('Fetch stats error:', error)
      }
    },
    handleSearch() {
      this.pagination.page = 1
      this.fetchOrderList()
      this.fetchStats()
    },
    handleReset() {
      this.searchForm = {
        order_id: '',
        status: '',
        mch_no: ''
      }
      this.dateRange = []
      this.sortField = ''
      this.sortOrder = ''
      this.pagination.page = 1
      this.fetchOrderList()
      this.fetchStats()
    },
    async handleExport() {
      this.exporting = true
      try {
        const params = { ...this.searchForm }
        if (this.dateRange && this.dateRange.length === 2) {
          params.created_at_start = this.dateRange[0]
          params.created_at_end = this.dateRange[1]
        }
        
        const response = await exportOrders(params)
        
        // 创建下载链接
        const blob = new Blob([response], { 
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
        })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `订单列表_${new Date().toISOString().slice(0, 10)}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        this.$message.success('导出成功')
      } catch (error) {
        console.error('Export error:', error)
        this.$message.error('导出失败')
      } finally {
        this.exporting = false
      }
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.fetchOrderList()
    },
    handleCurrentChange(page) {
      this.pagination.page = page
      this.fetchOrderList()
    },
    handleSortChange({ column, prop, order }) {
      this.sortField = prop
      this.sortOrder = order === 'ascending' ? 'asc' : 'desc'
      this.fetchOrderList()
    },
    viewDetail(row) {
      this.$router.push(`/order/detail/${row.order_id}`)
    },
    getStatusText(status) {
      return ORDER_STATUS[status]?.text || '未知'
    },
    getStatusType(status) {
      return ORDER_STATUS[status]?.type || 'info'
    },
    getPayMethodText(method) {
      const methodMap = {
        1: '外卡支付',
        2: 'USDT支付',
        3: '本地支付'
      }
      return methodMap[method] || '未知'
    },
    formatMoney(amount) {
      return Number(amount || 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.order-list {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #333;
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stats-item {
  text-align: center;
}

.stats-content {
  padding: 10px 0;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #666;
}

.table-card {
  margin-bottom: 20px;
}

.amount-text {
  font-weight: 600;
  color: #67C23A;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

@media (max-width: 768px) {
  .search-form .el-form-item {
    margin-bottom: 10px;
  }
  
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .pagination-wrapper {
    text-align: center;
  }
}
</style>