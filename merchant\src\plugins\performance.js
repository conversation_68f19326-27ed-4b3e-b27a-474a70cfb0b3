import { performanceMonitor, debounce, throttle, lazyLoadImages } from '@/utils/performance'

const PerformancePlugin = {
  install(Vue, options = {}) {
    // Add performance monitoring to Vue prototype
    Vue.prototype.$performance = performanceMonitor
    
    // Add utility functions
    Vue.prototype.$debounce = debounce
    Vue.prototype.$throttle = throttle
    
    // Global mixin for performance optimization
    Vue.mixin({
      beforeCreate() {
        // Track component creation time
        this._createTime = performance.now()
      },
      mounted() {
        // Track component mount time
        const mountTime = performance.now() - this._createTime
        if (mountTime > 100) { // Log slow components
          console.warn(`Slow component mount: ${this.$options.name || 'Anonymous'} took ${mountTime.toFixed(2)}ms`)
        }
        
        // Initialize lazy loading for images in this component
        this.$nextTick(() => {
          lazyLoadImages()
        })
      },
      beforeDestroy() {
        // Cleanup performance observers if any
        if (this._performanceObserver) {
          this._performanceObserver.disconnect()
        }
      }
    })
    
    // Directive for lazy loading
    Vue.directive('lazy', {
      bind(el, binding) {
        if (el.tagName.toLowerCase() === 'img') {
          el.dataset.src = binding.value
          el.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNGNUY3RkEiLz48L3N2Zz4='
          el.classList.add('lazy')
        }
      },
      inserted(el) {
        if ('IntersectionObserver' in window) {
          const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const img = entry.target
                img.src = img.dataset.src
                img.classList.remove('lazy')
                img.classList.add('loaded')
                observer.unobserve(img)
              }
            })
          })
          observer.observe(el)
          el._observer = observer
        } else {
          // Fallback
          el.src = el.dataset.src
          el.classList.remove('lazy')
          el.classList.add('loaded')
        }
      },
      unbind(el) {
        if (el._observer) {
          el._observer.disconnect()
        }
      }
    })
    
    // Directive for performance monitoring
    Vue.directive('perf', {
      bind(el, binding) {
        const startTime = performance.now()
        el._perfStart = startTime
        
        if (binding.value && binding.value.name) {
          console.log(`Performance tracking started for: ${binding.value.name}`)
        }
      },
      inserted(el, binding) {
        const endTime = performance.now()
        const duration = endTime - el._perfStart
        
        if (binding.value && binding.value.name) {
          console.log(`Performance tracking for ${binding.value.name}: ${duration.toFixed(2)}ms`)
          
          if (binding.value.threshold && duration > binding.value.threshold) {
            console.warn(`Performance threshold exceeded for ${binding.value.name}: ${duration.toFixed(2)}ms > ${binding.value.threshold}ms`)
          }
        }
      }
    })
    
    // Global error handler for performance issues
    Vue.config.errorHandler = function(err, vm, info) {
      console.error('Vue error:', err, info)
      
      // Report performance-related errors
      if (err.message.includes('performance') || err.message.includes('memory')) {
        performanceMonitor.reportMetrics('error', {
          message: err.message,
          component: vm.$options.name || 'Unknown',
          info: info
        })
      }
    }
    
    // Add global performance methods
    Vue.prototype.$trackPerformance = function(name, fn) {
      const start = performance.now()
      const result = fn()
      const end = performance.now()
      
      console.log(`${name} took ${(end - start).toFixed(2)}ms`)
      return result
    }
    
    Vue.prototype.$measureComponent = function(name) {
      const start = performance.now()
      return () => {
        const end = performance.now()
        console.log(`Component ${name} lifecycle took ${(end - start).toFixed(2)}ms`)
      }
    }
  }
}

export default PerformancePlugin